<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Elephant Watering System — Flowchart</title>
    <style>
      :root { color-scheme: dark; }
      body { font-family: system-ui, Segoe UI, Arial, sans-serif; background:#0b1021; color:#eaeefb; margin:0; padding:24px; }
      .card { background:#1a1f36; border-radius:14px; padding:18px; box-shadow: 0 6px 24px rgba(0,0,0,0.35); }
      h1 { margin:0 0 16px 0; font-weight:600; letter-spacing:0.2px; }
      .hint { margin-top:12px; font-size:0.9rem; opacity:0.8; }
      .mermaid { overflow-x:auto; }
    </style>
    <!-- UMD build works well for opening local files directly -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
      mermaid.initialize({ startOnLoad: true, theme: 'dark', securityLevel: 'loose' });
    </script>
  </head>
  <body>
    <h1>Elephant Watering System — Flowchart</h1>
    <div class="card">
      <div class="mermaid">
flowchart TD
  S((Start)) --> L[Main loop]
  L --> C_DHT{DHT due: 2s elapsed}
  C_DHT -->|yes| N_DHT[Read DHT11; update temperature and humidity]
  C_DHT -->|no| N_Soil[Read A0 soil value]
  N_DHT --> N_Soil
  N_Soil --> N_Smooth[soilAvg = 0.9 soilAvg + 0.1 soilValue]
  N_Smooth --> N_Stab[Update drySince and wetSince]
  N_Stab --> C_ON{Pump OFF and dryStable and offTime >= 5s}
  C_ON -->|yes| A_ON[Pump ON; relay HIGH; lastPumpToggle = now]
  C_ON -->|no| C_OFF{Pump ON and wetStable and onTime >= 3s}
  A_ON --> C_OFF
  C_OFF -->|yes| A_OFF[Pump OFF; relay LOW; lastPumpToggle = now]
  C_OFF -->|no| C_SAFE{Pump ON and onTime >= 10s}
  A_OFF --> C_SAFE
  C_SAFE -->|yes| A_OFF
  C_SAFE -->|no| N_Out[Every 100ms: print metrics]
  N_Out --> L
      </div>
      <p class="hint">Tip: You can edit the text above directly to tweak the diagram.</p>
    </div>
  </body>
</html>