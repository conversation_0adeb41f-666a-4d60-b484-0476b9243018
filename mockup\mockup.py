# pip install reportlab
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm
from math import pi

# ---------- adjustable dims (mm) ----------
LCD_BEZEL_W = 80.0
LCD_BEZEL_H = 36.0
LCD_WIN_W   = 64.0
LCD_WIN_H   = 14.0

BTN_D       = 12.0
BTN_SPACING = 22.0
BTN_OFFSET_Y= 18.0

PANEL_W     = 110.0
PANEL_H     = 90.0
MARGIN      = 10.0

out_path = "Elephant_Hat_UI_Paper_Mockup.pdf"
# -----------------------------------------

PAGE_W, PAGE_H = A4  # points
PAGE_W_MM, PAGE_H_MM = PAGE_W/mm, PAGE_H/mm

def draw_ruler(c, x_mm, y_mm):
    x = x_mm*mm; y = y_mm*mm
    c.setLineWidth(1)
    c.line(x, y, (x+100*mm), y)
    for i in range(0, 101, 10):
        c.line(x+i*mm, y-3, x+i*mm, y+3)
        c.setFont("Helvetica", 7)
        c.drawCentredString(x+i*mm, y+5, f"{i} mm")
    c.setLineWidth(0.5)
    for i in range(101):
        tick = 1.5 if i % 5 else 2.5
        c.line(x+i*mm, y-tick, x+i*mm, y+tick)

def page1(c):
    c.setFont("Helvetica-Bold", 10)
    c.drawString(MARGIN*mm, (PAGE_H_MM - MARGIN/2)*mm, "Elephant Hat — Paper UI (PRINT AT 100% SCALE)")

    # ruler
    draw_ruler(c, MARGIN, PAGE_H_MM - 15)

    # panel rect centered
    px = (PAGE_W_MM - PANEL_W)/2
    py = (PAGE_H_MM/2 - PANEL_H/2)
    c.setLineWidth(1.2)
    c.rect(px*mm, py*mm, PANEL_W*mm, PANEL_H*mm, stroke=1, fill=0)
    c.setFont("Helvetica", 8)
    c.drawString(px*mm, (py-4)*mm, f"Panel {PANEL_W:.0f} × {PANEL_H:.0f} mm — tape this on the hat")

    # bezel centered near top of panel
    bx = px + (PANEL_W - LCD_BEZEL_W)/2
    by = py + PANEL_H - LCD_BEZEL_H - 20
    c.rect(bx*mm, by*mm, LCD_BEZEL_W*mm, LCD_BEZEL_H*mm, stroke=1, fill=0)
    c.drawString(bx*mm, (by + LCD_BEZEL_H + 3)*mm, f"LCD bezel {LCD_BEZEL_W:.0f} × {LCD_BEZEL_H:.0f} mm")

    # window (dashed)
    wx = bx + (LCD_BEZEL_W - LCD_WIN_W)/2
    wy = by + (LCD_BEZEL_H - LCD_WIN_H)/2
    c.setDash(3,2)
    c.rect(wx*mm, wy*mm, LCD_WIN_W*mm, LCD_WIN_H*mm, stroke=1, fill=0)
    c.setDash()  # reset
    c.drawString(wx*mm, (wy-3)*mm, f"LCD window (cut) {LCD_WIN_W:.0f} × {LCD_WIN_H:.0f} mm")

    # fake on-screen text
    c.setFont("Helvetica", 8)
    c.drawString((wx+2)*mm, (wy + LCD_WIN_H*0.65)*mm, "Soil 38%   Status: OK")
    c.drawString((wx+2)*mm, (wy + LCD_WIN_H*0.20)*mm, "Next run 07:30")

    # buttons (centered)
    btn_cx = px + PANEL_W/2
    btn_cy = wy - BTN_OFFSET_Y
    b1x = btn_cx - BTN_SPACING/2
    b2x = btn_cx + BTN_SPACING/2
    for bx_, label in [(b1x, "Water"), (b2x, "Menu/Select")]:
        c.circle(bx_*mm, btn_cy*mm, (BTN_D/2)*mm, stroke=1, fill=0)
        c.setFont("Helvetica", 7)
        c.drawCentredString(bx_*mm, (btn_cy - BTN_D/2 - 3)*mm, f"{label}  Ø{BTN_D:.0f} mm")

    # notes
    c.setFont("Helvetica", 8)
    notes = ("Print instructions:\n"
             "• Print at 100% scale (no fit-to-page). Verify the 0–100 mm ruler with a real ruler.\n"
             "• Tape this panel on the hat to check size, readability, and reach.\n"
             "• If it feels off, adjust CAD and reprint.")
    text_y = (py + 10)*mm
    for line in notes.split("\n"):
        c.drawString(px*mm, text_y, line); text_y -= 10

def screen_card(c, x_mm, y_mm, title, line1, line2):
    pad = 10.0
    # outer card
    c.setLineWidth(1.0)
    c.rect(x_mm*mm, (y_mm - (LCD_WIN_H + 2*pad))*mm, (LCD_WIN_W + 2*pad)*mm, (LCD_WIN_H + 2*pad)*mm, stroke=1, fill=0)
    # inner window (dashed)
    wx2 = x_mm + pad
    wy2 = y_mm - LCD_WIN_H
    c.setDash(3,2)
    c.rect(wx2*mm, wy2*mm, LCD_WIN_W*mm, LCD_WIN_H*mm, stroke=1, fill=0)
    c.setDash()
    # labels
    c.setFont("Helvetica-Bold", 9)
    c.drawString(x_mm*mm, (y_mm + 5)*mm, title)
    c.setFont("Helvetica", 8)
    c.drawString((wx2+2)*mm, (wy2 + LCD_WIN_H*0.65)*mm, line1)
    c.drawString((wx2+2)*mm, (wy2 + LCD_WIN_H*0.20)*mm, line2)
    c.setFont("Helvetica", 7)
    c.drawString(x_mm*mm, (wy2 - 8)*mm, "Cut outer box; slide behind the window on Page 1.")

def page2(c):
    c.setFont("Helvetica-Bold", 10)
    c.drawString(MARGIN*mm, (PAGE_H_MM - MARGIN/2)*mm, "Swap-in cards (cut and place behind window)")
    sx = MARGIN; sy = PAGE_H_MM - 40
    cards = [
        ("Idle",      "Soil 41%   Status: OK", "Next run 07:30"),
        ("Watering",  "PUMP ON   12s left",    "Moisture 29%"),
        ("Low water", "ERROR: Low water",      "Refill reservoir"),
    ]
    for i, (t, l1, l2) in enumerate(cards):
        y = sy - i*(LCD_WIN_H + 2*10 + 22)
        screen_card(c, sx, y, t, l1, l2)

def main():
    c = canvas.Canvas(out_path, pagesize=A4)
    page1(c); c.showPage()
    page2(c); c.showPage()
    c.save()
    print("Saved:", out_path)

if __name__ == "__main__":
    main()
