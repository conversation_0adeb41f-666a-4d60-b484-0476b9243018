<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>UI Components — Mockup Viewer</title>
  <style>
    body { 
      margin: 0; 
      padding: 0; 
      background: #0f0816; 
      font-family: 'Inter','Segoe UI',sans-serif; 
      color: #e6dbff;
    }
    .viewer { 
      width: min(920px, 92vw); 
      margin: 40px auto; 
      background: linear-gradient(180deg, rgba(32,18,44,0.7), rgba(18,10,30,0.55));
      border-radius: 18px; 
      padding: 24px; 
      box-shadow: 0 10px 40px rgba(95,30,110,0.25); 
      border: 1px solid rgba(140,100,190,0.12); 
    }
    h1 { text-align: center; font-weight: 800; font-size: 1.8rem; margin: 0 0 12px; }
    p { text-align: center; color: #d9cffb; margin: 0 0 18px; }
    .sheet { width: 100%; height: auto; display: block; border-radius: 12px; }
    @media print { 
      body { background: white; }
      .viewer { box-shadow: none; border: none; background: white; }
      .sheet { width: 100%; }
    }
  </style>
</head>
<body>
  <div class="viewer">
    <h1>UI Component Library (Dark)</h1>
    <p>Print-ready SVG embedded below — matches plotter theme.</p>
    <img src="./ui-components.svg" alt="UI Components" class="sheet" />
  </div>
</body>
</html>