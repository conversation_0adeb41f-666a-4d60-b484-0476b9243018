<!DOCTYPE html>
<html>
  <head>
    <script type="text/javascript" src="../smoothie.js"></script>
    <script type="text/javascript">

      // Randomly add a data point every 500ms
      var random = new TimeSeries();
      setInterval(function() {
        random.append(Date.now(), Math.random() * 10000);
      }, 500);
      
      function createTimeline() {
        var chart = new SmoothieChart();
        chart.addTimeSeries(random, { strokeStyle: 'rgba(0, 255, 0, 1)', fillStyle: 'rgba(0, 255, 0, 0.2)', lineWidth: 4 });
        chart.streamTo(document.getElementById("chart"), 500);
      }
    </script>
  </head>
  <body onload="createTimeline()" style="background-color:#333333">

    <canvas id="chart" width="100" height="100"></canvas>

  </body>
</html>
