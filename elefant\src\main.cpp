/*
  Simple Motor/Pump Test - Elephant Watering System
  
  This test file isolates the motor/pump control to verify relay functionality.
  
  Hardware setup:
    - Pin D8 → Relay IN (5V relay module)
    - Arduino 5V → Relay VCC
    - Arduino GND → Relay GND
    - Pump+ → Relay COM
    - External supply+ → Relay NO
    - Pump- → External supply GND
    
  Test sequence:
    - Motor ON for 3 seconds
    - Motor OFF for 3 seconds
    - Repeat cycle
    
  Serial output shows motor status for monitoring.
*/

#include <Arduino.h>

const int RELAY_PIN = 8;             // Digital pin for relay IN
const bool RELAY_ACTIVE_LOW = true;  // Most relay modules are ACTIVE LOW

// Timing variables
unsigned long lastToggle = 0;
const unsigned long CYCLE_TIME = 3000;  // 3 seconds ON, 3 seconds OFF
bool motorState = false;

// Helper function to control relay with proper polarity
void setMotor(bool on) {
  digitalWrite(RELAY_PIN, (RELAY_ACTIVE_LOW ? (on ? LOW : HIGH) : (on ? HIGH : LOW)));
  motorState = on;
}

void setup() {
  Serial.begin(9600);
  pinMode(RELAY_PIN, OUTPUT);
  
  // Start with motor OFF
  setMotor(false);
  
  Serial.println("=== MOTOR/PUMP TEST ===");
  Serial.println("Testing relay on pin D8");
  Serial.println("Cycle: 3 sec ON, 3 sec OFF");
  Serial.println("=======================");
  
  lastToggle = millis();
}

void loop() {
  unsigned long currentTime = millis();
  
  // Toggle motor every 3 seconds
  if (currentTime - lastToggle >= CYCLE_TIME) {
    setMotor(!motorState);  // Toggle state
    
    // Print status
    Serial.print("Motor: ");
    Serial.print(motorState ? "ON " : "OFF");
    Serial.print(" | PWM: ");
    Serial.print(motorState ? "255" : "0");
    Serial.print(" | Relay Pin D8: ");
    Serial.print(digitalRead(RELAY_PIN) ? "HIGH" : "LOW");
    Serial.print(" | Time: ");
    Serial.print(currentTime / 1000);
    Serial.println("s");
    
    lastToggle = currentTime;
  }
  
  // Small delay to avoid overwhelming serial output
  delay(100);
}
