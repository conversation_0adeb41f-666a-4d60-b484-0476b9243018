<svg width="680" height="900" viewBox="0 0 680 900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 22px; font-weight: 800; fill: #e6dbff; }
      .subtitle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 12px; font-weight: 500; fill: #d9cffb; }
      .card-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 15px; font-weight: 700; fill: #b48cff; }
      .metric-large { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 20px; font-weight: 800; fill: #e9e6f7; }
      .metric-small { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 12px; font-weight: 600; fill: #cfc7ee; }
      .status-text { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 11px; font-weight: 700; }

      .card { fill: rgba(32,18,44,0.8); stroke: rgba(140,100,190,0.20); stroke-width: 1; }
      .chart-area { fill: rgba(255,255,255,0.03); stroke: rgba(140,100,190,0.18); stroke-width: 1; }

      .status-normal { fill: rgba(39,174,96,0.25); }
      .status-warning { fill: rgba(247,183,49,0.25); }
      .status-active { fill: rgba(102,126,234,0.28); }

      .icon-bg { fill: rgba(180,140,255,0.25); }
    </style>
  </defs>

  <!-- Background -->
  <rect width="680" height="900" fill="#0f0816"/>

  <!-- Header -->
  <text x="340" y="40" text-anchor="middle" class="title">Elephant Watering System — UI Components (Dark)</text>
  <text x="340" y="60" text-anchor="middle" class="subtitle">Glassy cards · Purple accents · Print friendly</text>

  <!-- Status Bar (glassy) -->
  <rect x="60" y="80" width="560" height="50" rx="16" fill="rgba(32,18,44,0.75)" stroke="rgba(140,100,190,0.20)"/>
  <circle cx="84" cy="105" r="5" fill="#27ae60"/>
  <text x="100" y="110" class="metric-small" fill="#cfe9d8">Connected (COM4 @ 9600)</text>
  <text x="600" y="110" text-anchor="end" class="metric-small" fill="#d9cffb">14:32:15</text>

  <!-- Column 1: Temperature, Soil ADC, Servo -->
  <!-- Temperature Card -->
  <rect x="60" y="150" width="260" height="170" rx="18" class="card"/>
  <rect x="76" y="166" width="18" height="18" rx="5" class="icon-bg"/>
  <text x="102" y="178" class="card-title">Temperature (°C)</text>
  <rect x="76" y="192" width="228" height="84" rx="10" class="chart-area"/>
  <text x="190" y="236" text-anchor="middle" class="subtitle">Smoothie chart style</text>
  <text x="76" y="292" class="metric-large">23.4</text>
  <text x="118" y="292" class="metric-small">°C</text>
  <rect x="212" y="277" width="72" height="22" rx="7" class="status-normal"/>
  <text x="248" y="292" text-anchor="middle" class="status-text" fill="#27ae60">Normal</text>

  <!-- Soil Moisture (ADC) Card -->
  <rect x="60" y="330" width="260" height="170" rx="18" class="card"/>
  <rect x="76" y="346" width="18" height="18" rx="5" class="icon-bg"/>
  <text x="102" y="358" class="card-title">Soil Moisture (ADC)</text>
  <rect x="76" y="372" width="228" height="84" rx="10" class="chart-area"/>
  <text x="190" y="416" text-anchor="middle" class="subtitle">Raw readings</text>
  <text x="76" y="472" class="metric-large">572</text>
  <text x="118" y="472" class="metric-small">ADC</text>
  <rect x="212" y="457" width="72" height="22" rx="7" class="status-warning"/>
  <text x="248" y="472" text-anchor="middle" class="status-text" fill="#f39c12">Dry</text>

  <!-- Servo Position Card -->
  <rect x="60" y="510" width="260" height="170" rx="18" class="card"/>
  <rect x="76" y="526" width="18" height="18" rx="5" class="icon-bg"/>
  <text x="102" y="538" class="card-title">Servo Angle (°)</text>
  <rect x="76" y="552" width="228" height="84" rx="10" class="chart-area"/>
  <text x="190" y="596" text-anchor="middle" class="subtitle">Position</text>
  <text x="76" y="652" class="metric-large">90</text>
  <text x="106" y="652" class="metric-small">°</text>
  <rect x="212" y="637" width="72" height="22" rx="7" class="status-normal"/>
  <text x="248" y="652" text-anchor="middle" class="status-text" fill="#27ae60">Center</text>

  <!-- Column 2: Humidity, Soil %, Pump -->
  <!-- Humidity Card -->
  <rect x="360" y="150" width="260" height="170" rx="18" class="card"/>
  <rect x="376" y="166" width="18" height="18" rx="5" class="icon-bg"/>
  <text x="402" y="178" class="card-title">Humidity (%)</text>
  <rect x="376" y="192" width="228" height="84" rx="10" class="chart-area"/>
  <text x="490" y="236" text-anchor="middle" class="subtitle">Levels</text>
  <text x="376" y="292" class="metric-large">56</text>
  <text x="408" y="292" class="metric-small">%</text>
  <rect x="512" y="277" width="72" height="22" rx="7" class="status-normal"/>
  <text x="548" y="292" text-anchor="middle" class="status-text" fill="#27ae60">Normal</text>

  <!-- Soil Moisture (%) Card -->
  <rect x="360" y="330" width="260" height="170" rx="18" class="card"/>
  <rect x="376" y="346" width="18" height="18" rx="5" class="icon-bg"/>
  <text x="402" y="358" class="card-title">Soil Moisture (%)</text>
  <rect x="376" y="372" width="228" height="84" rx="10" class="chart-area"/>
  <text x="490" y="416" text-anchor="middle" class="subtitle">Derived from ADC</text>
  <text x="376" y="472" class="metric-large">62</text>
  <text x="408" y="472" class="metric-small">%</text>
  <rect x="512" y="457" width="72" height="22" rx="7" class="status-warning"/>
  <text x="548" y="472" text-anchor="middle" class="status-text" fill="#f39c12">Low</text>

  <!-- Pump Control Card -->
  <rect x="360" y="510" width="260" height="170" rx="18" class="card"/>
  <rect x="376" y="526" width="18" height="18" rx="5" class="icon-bg"/>
  <text x="402" y="538" class="card-title">Motor / Pump PWM</text>
  <rect x="376" y="552" width="228" height="84" rx="10" class="chart-area"/>
  <text x="490" y="596" text-anchor="middle" class="subtitle">Signal</text>
  <text x="376" y="652" class="metric-large">255</text>
  <text x="418" y="652" class="metric-small">PWM</text>
  <rect x="512" y="637" width="72" height="22" rx="7" class="status-active"/>
  <text x="548" y="652" text-anchor="middle" class="status-text" fill="#667eea">Active</text>

  <!-- Footer -->
  <rect x="60" y="720" width="560" height="70" rx="12" fill="rgba(102,126,234,0.12)" stroke="#667eea"/>
  <text x="340" y="745" text-anchor="middle" class="card-title">Dark Glassy Theme Components</text>
  <text x="340" y="765" text-anchor="middle" class="subtitle">Matches serial plotter aesthetics</text>
  <text x="340" y="785" text-anchor="middle" class="subtitle">Use for print-ready mockups</text>
</svg>