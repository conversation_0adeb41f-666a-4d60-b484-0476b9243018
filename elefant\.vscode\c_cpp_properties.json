//
// !!! WARNING !!! AUTO-GENERATED FILE!
// PLEASE DO NOT MODIFY IT AND USE "platformio.ini":
// https://docs.platformio.org/page/projectconf/section_env_build.html#build-flags
//
{
    "configurations": [
        {
            "name": "PlatformIO",
            "includePath": [
                "C:/Users/<USER>/Documents/PlatformIO/Projects/elefant/include",
                "C:/Users/<USER>/Documents/PlatformIO/Projects/elefant/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/cores/arduino",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/variants/standard",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/EEPROM/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/HID/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/SPI/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/SoftwareSerial/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/Wire/src",
                ""
            ],
            "browse": {
                "limitSymbolsToIncludedHeaders": true,
                "path": [
                    "C:/Users/<USER>/Documents/PlatformIO/Projects/elefant/include",
                    "C:/Users/<USER>/Documents/PlatformIO/Projects/elefant/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/cores/arduino",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/variants/standard",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/EEPROM/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/HID/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/SPI/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/SoftwareSerial/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/Wire/src",
                    ""
                ]
            },
            "defines": [
                "PLATFORMIO=60118",
                "ARDUINO_AVR_UNO",
                "F_CPU=16000000L",
                "ARDUINO_ARCH_AVR",
                "ARDUINO=10808",
                "__AVR_ATmega328P__",
                ""
            ],
            "cStandard": "gnu11",
            "cppStandard": "gnu++11",
            "compilerPath": "C:/Users/<USER>/.platformio/packages/toolchain-atmelavr/bin/avr-gcc.exe",
            "compilerArgs": [
                "-mmcu=atmega328p",
                ""
            ]
        }
    ],
    "version": 4
}
