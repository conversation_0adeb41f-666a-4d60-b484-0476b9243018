<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Elephant Watering System — Mockup (Dark Glassy)</title>
  <style>
    /* Dark glassy theme to match fan_plotter */
    :root {
      --bg: #0f0816;
      --panel-grad-top: rgba(32,18,44,0.7);
      --panel-grad-bottom: rgba(18,10,30,0.55);
      --panel-border: rgba(140,100,190,0.12);
      --card-border: rgba(120,80,200,0.06);
      --card-glass-top: rgba(255,255,255,0.02);
      --card-glass-bottom: rgba(255,255,255,0.01);
      --text-main: #e6dbff;
      --text-sub: #d9cffb;
      --accent: #b48cff;
    }

    body {
      margin: 0;
      padding: 0;
      font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: var(--bg);
      color: var(--text-main);
    }

    .glassy-mock {
      width: min(920px, 92vw);
      margin: 48px auto;
      background: linear-gradient(180deg, var(--panel-grad-top), var(--panel-grad-bottom));
      border-radius: 18px;
      padding: 28px 26px 30px 26px;
      box-shadow: 0 10px 40px rgba(95,30,110,0.25);
      backdrop-filter: blur(12px) saturate(1.1);
      border: 1px solid var(--panel-border);
    }

    .badge {
      display: inline-flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 12px;
      padding: 8px 18px;
      border-radius: 16px;
      color: #f0eaff;
      background: linear-gradient(180deg, rgba(255,255,255,0.06), rgba(255,255,255,0.03));
      box-shadow: inset 0 1px 0 rgba(255,255,255,0.12);
      border: 1px solid rgba(140,100,190,0.16);
      font-weight: 700;
      font-size: 0.9rem;
    }

    h1 {
      color: var(--text-main);
      text-align: center;
      margin: 8px 0 6px;
      font-size: 1.9rem;
      font-weight: 800;
      letter-spacing: 0.02em;
    }
    .sub {
      text-align: center;
      color: var(--text-sub);
      margin-bottom: 22px;
      font-size: 0.98rem;
      font-weight: 500;
    }

    .status {
      text-align: center;
      margin-bottom: 18px;
      color: #27ae60;
      font-weight: 800;
    }

    .grid {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 20px;
    }
    @media (min-width: 820px) {
      .grid { grid-template-columns: 1fr; }
    }

    .card {
      border-radius: 12px;
      background: linear-gradient(180deg, var(--card-glass-top), var(--card-glass-bottom));
      box-shadow: inset 0 1px 0 rgba(255,255,255,0.02), 0 6px 16px rgba(40,10,60,0.18);
      border: 1px solid var(--card-border);
      backdrop-filter: blur(4px) saturate(1.02);
      padding: 18px;
    }
    .card h2 {
      color: var(--accent);
      margin: 0 0 12px;
      font-size: 1.5em;
      font-weight: 700;
    }
    .chart-area {
      height: 170px;
      background: rgba(255,255,255,0.03);
      border: 2px dashed rgba(140,100,190,0.18);
      border-radius: 12px;
      display: flex; align-items: center; justify-content: center;
      color: var(--text-sub);
      font-weight: 600;
      margin-bottom: 12px;
    }
    .metric { text-align: left; font-size: 16px; color: rgba(230,225,255,0.85); font-weight: 600; }

    .footer { text-align: center; margin-top: 24px; color: rgba(220,210,255,0.78); font-size: 12px; }

    @media print {
      .glassy-mock { margin: 10mm auto; }
      .chart-area { height: 140px; }
      .card { break-inside: avoid; }
    }
  </style>
</head>
<body>
  <div class="glassy-mock">
    <div class="badge">Made by Carlos Carpio 🤓</div>
    <h1>Elephant Watering System <span style="filter:drop-shadow(0 0 8px #b48cff)">☄️</span></h1>
    <p class="sub">Arduino Uno | DHT11 D2 | Soil Moisture A0 | Relay D8 | Mini Pump 🔌⚡</p>

    <div class="status">Status: Connected (COM4)</div>

    <div class="grid">
      <div class="card">
        <h2>Temperature (°C)</h2>
        <div class="chart-area">Smoothie chart placeholder</div>
        <p class="metric">Current: 22.0 °C</p>
      </div>

      <div class="card">
        <h2>Humidity (%)</h2>
        <div class="chart-area">Smoothie chart placeholder</div>
        <p class="metric">Current: 56 %</p>
      </div>

      <div class="card">
        <h2>Soil Moisture (ADC Value)</h2>
        <div class="chart-area">Smoothie chart placeholder</div>
        <p class="metric">Current: 572 ADC</p>
      </div>

      <div class="card">
        <h2>Soil Moisture (%)</h2>
        <div class="chart-area">Smoothie chart placeholder</div>
        <p class="metric">Current: 62 %</p>
      </div>

      <div class="card">
        <h2>Servo Angle (°)</h2>
        <div class="chart-area">Smoothie chart placeholder</div>
        <p class="metric">Current: 90°</p>
      </div>

      <div class="card">
        <h2>Motor / Pump PWM</h2>
        <div class="chart-area">Smoothie chart placeholder</div>
        <p class="metric">Current: 255 PWM (ON)</p>
      </div>
    </div>

    <div class="footer">Print this dark glassy mockup (A4). Matches the real plotter UI for studio presentation.</div>
  </div>
</body>
</html>