export {
  // Type-only exports
  AcceptedPlugin,

  AnyNode,
  atRule,
  AtRule,
  AtRuleProps,
  Builder,
  ChildNode,
  ChildProps,
  comment,
  Comment,
  CommentProps,
  Container,
  ContainerProps,
  CssSyntaxError,
  decl,
  Declaration,
  DeclarationProps,
  // postcss function / namespace
  default,
  document,
  Document,
  DocumentProps,
  FilePosition,
  fromJSON,
  Helpers,
  Input,

  JSONHydrator,
  // This is a class, but it’s not re-exported. That’s why it’s exported as type-only here.
  type LazyResult,
  list,
  Message,
  Node,
  NodeErrorOptions,
  NodeProps,
  OldPlugin,
  parse,
  Parser,
  // @ts-expect-error This value exists, but it’s untyped.
  plugin,
  Plugin,
  PluginCreator,
  Position,
  Postcss,
  ProcessOptions,
  Processor,
  Result,
  root,
  Root,
  RootProps,
  rule,
  Rule,
  RuleProps,
  Source,
  SourceMap,
  SourceMapOptions,
  Stringifier,
  // Value exports from postcss.mjs
  stringify,
  Syntax,
  TransformCallback,
  Transformer,
  Warning,

  WarningOptions
} from './postcss.js'
