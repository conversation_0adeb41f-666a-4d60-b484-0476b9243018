{"version": 3, "sources": ["../../smoothie/smoothie.js"], "sourcesContent": [";(function(exports) {\r\n\r\n/**\r\n * @license\r\n * MIT License:\r\n *\r\n * Copyright (c) 2010-2013, <PERSON>\r\n *               2013-2018, <PERSON>\r\n *\r\n * Permission is hereby granted, free of charge, to any person obtaining a copy\r\n * of this software and associated documentation files (the \"Software\"), to deal\r\n * in the Software without restriction, including without limitation the rights\r\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\n * copies of the Software, and to permit persons to whom the Software is\r\n * furnished to do so, subject to the following conditions:\r\n *\r\n * The above copyright notice and this permission notice shall be included in\r\n * all copies or substantial portions of the Software.\r\n *\r\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\n * THE SOFTWARE.\r\n */\r\n\r\n/**\r\n * Smoothie Charts - http://smoothiecharts.org/\r\n * (c) 2010-2013, Joe Walnes\r\n *     2013-2018, Drew Noakes\r\n *\r\n * v1.0: Main charting library, by Joe Walnes\r\n * v1.1: Auto scaling of axis, by <PERSON> <PERSON>\r\n * v1.2: fps (frames per second) option, by <PERSON> Petterson\r\n * v1.3: Fix for divide by zero, by Paul Nikitochkin\r\n * v1.4: Set minimum, top-scale padding, remove timeseries, add optional timer to reset bounds, by Kelley Reynolds\r\n * v1.5: Set default frames per second to 50... smoother.\r\n *       .start(), .stop() methods for conserving CPU, by Dmitry Vyal\r\n *       options.interpolation = 'bezier' or 'line', by Dmitry Vyal\r\n *       options.maxValue to fix scale, by Dmitry Vyal\r\n * v1.6: minValue/maxValue will always get converted to floats, by Przemek Matylla\r\n * v1.7: options.grid.fillStyle may be a transparent color, by Dmitry A. Shashkin\r\n *       Smooth rescaling, by Kostas Michalopoulos\r\n * v1.8: Set max length to customize number of live points in the dataset with options.maxDataSetLength, by Krishna Narni\r\n * v1.9: Display timestamps along the bottom, by Nick and Stev-io\r\n *       (https://groups.google.com/forum/?fromgroups#!topic/smoothie-charts/-Ywse8FCpKI%5B1-25%5D)\r\n *       Refactored by Krishna Narni, to support timestamp formatting function\r\n * v1.10: Switch to requestAnimationFrame, removed the now obsoleted options.fps, by Gergely Imreh\r\n * v1.11: options.grid.sharpLines option added, by @drewnoakes\r\n *        Addressed warning seen in Firefox when seriesOption.fillStyle undefined, by @drewnoakes\r\n * v1.12: Support for horizontalLines added, by @drewnoakes\r\n *        Support for yRangeFunction callback added, by @drewnoakes\r\n * v1.13: Fixed typo (#32), by @alnikitich\r\n * v1.14: Timer cleared when last TimeSeries removed (#23), by @davidgaleano\r\n *        Fixed diagonal line on chart at start/end of data stream, by @drewnoakes\r\n * v1.15: Support for npm package (#18), by @dominictarr\r\n *        Fixed broken removeTimeSeries function (#24) by @davidgaleano\r\n *        Minor performance and tidying, by @drewnoakes\r\n * v1.16: Bug fix introduced in v1.14 relating to timer creation/clearance (#23), by @drewnoakes\r\n *        TimeSeries.append now deals with out-of-order timestamps, and can merge duplicates, by @zacwitte (#12)\r\n *        Documentation and some local variable renaming for clarity, by @drewnoakes\r\n * v1.17: Allow control over font size (#10), by @drewnoakes\r\n *        Timestamp text won't overlap, by @drewnoakes\r\n * v1.18: Allow control of max/min label precision, by @drewnoakes\r\n *        Added 'borderVisible' chart option, by @drewnoakes\r\n *        Allow drawing series with fill but no stroke (line), by @drewnoakes\r\n * v1.19: Avoid unnecessary repaints, and fixed flicker in old browsers having multiple charts in document (#40), by @asbai\r\n * v1.20: Add SmoothieChart.getTimeSeriesOptions and SmoothieChart.bringToFront functions, by @drewnoakes\r\n * v1.21: Add 'step' interpolation mode, by @drewnoakes\r\n * v1.22: Add support for different pixel ratios. Also add optional y limit formatters, by @copacetic\r\n * v1.23: Fix bug introduced in v1.22 (#44), by @drewnoakes\r\n * v1.24: Fix bug introduced in v1.23, re-adding parseFloat to y-axis formatter defaults, by @siggy_sf\r\n * v1.25: Fix bug seen when adding a data point to TimeSeries which is older than the current data, by @Nking92\r\n *        Draw time labels on top of series, by @comolosabia\r\n *        Add TimeSeries.clear function, by @drewnoakes\r\n * v1.26: Add support for resizing on high device pixel ratio screens, by @copacetic\r\n * v1.27: Fix bug introduced in v1.26 for non whole number devicePixelRatio values, by @zmbush\r\n * v1.28: Add 'minValueScale' option, by @megawac\r\n *        Fix 'labelPos' for different size of 'minValueString' 'maxValueString', by @henryn\r\n * v1.29: Support responsive sizing, by @drewnoakes\r\n * v1.29.1: Include types in package, and make property optional, by @TrentHouliston\r\n * v1.30: Fix inverted logic in devicePixelRatio support, by @scanlime\r\n * v1.31: Support tooltips, by @Sly1024 and @drewnoakes\r\n * v1.32: Support frame rate limit, by @dpuyosa\r\n * v1.33: Use Date static method instead of instance, by @nnnoel\r\n *        Fix bug with tooltips when multiple charts on a page, by @jpmbiz70\r\n * v1.34: Add disabled option to TimeSeries, by @TechGuard (#91)\r\n *        Add nonRealtimeData option, by @annazhelt (#92, #93)\r\n *        Add showIntermediateLabels option, by @annazhelt (#94)\r\n *        Add displayDataFromPercentile option, by @annazhelt (#95)\r\n *        Fix bug when hiding tooltip element, by @ralphwetzel (#96)\r\n *        Support intermediate y-axis labels, by @beikeland (#99)\r\n * v1.35: Fix issue with responsive mode at high DPI, by @drewnoakes (#101)\r\n * v1.36: Add tooltipLabel to ITimeSeriesPresentationOptions.\r\n *        If tooltipLabel is present, tooltipLabel displays inside tooltip\r\n *        next to value, by @jackdesert (#102)\r\n *        Fix bug rendering issue in series fill when using scroll backwards, by @olssonfredrik\r\n *        Add title option, by @mesca\r\n *        Fix data drop stoppage by rejecting NaNs in append(), by @timdrysdale\r\n *        Allow setting interpolation per time series, by @WofWca (#123)\r\n *        Fix chart constantly jumping in 1-2 pixel steps, by @WofWca (#131)\r\n *        Fix a memory leak appearing when some `timeSeries.disabled === true`, by @WofWca (#132)\r\n *        Fix: make all lines sharp, remove the `grid.sharpLines` option by @WofWca (#134)\r\n *        Improve performance, by @WofWca (#135)\r\n *        Fix `this.delay` not being respected with `nonRealtimeData: true`, by @WofWca (#137)\r\n *        Fix series fill & stroke being inconsistent for last data time < render time, by @WofWca (#138)\r\n * v1.36.1: Fix a potential XSS when `tooltipLabel` or `strokeStyle` are controlled by users, by @WofWca\r\n */\r\n\r\n  // Date.now polyfill\r\n  Date.now = Date.now || function() { return new Date().getTime(); };\r\n\r\n  var Util = {\r\n    extend: function() {\r\n      arguments[0] = arguments[0] || {};\r\n      for (var i = 1; i < arguments.length; i++)\r\n      {\r\n        for (var key in arguments[i])\r\n        {\r\n          if (arguments[i].hasOwnProperty(key))\r\n          {\r\n            if (typeof(arguments[i][key]) === 'object') {\r\n              if (arguments[i][key] instanceof Array) {\r\n                arguments[0][key] = arguments[i][key];\r\n              } else {\r\n                arguments[0][key] = Util.extend(arguments[0][key], arguments[i][key]);\r\n              }\r\n            } else {\r\n              arguments[0][key] = arguments[i][key];\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return arguments[0];\r\n    },\r\n    binarySearch: function(data, value) {\r\n      var low = 0,\r\n          high = data.length;\r\n      while (low < high) {\r\n        var mid = (low + high) >> 1;\r\n        if (value < data[mid][0])\r\n          high = mid;\r\n        else\r\n          low = mid + 1;\r\n      }\r\n      return low;\r\n    },\r\n    // So lines (especially vertical and horizontal) look a) consistent along their length and b) sharp.\r\n    pixelSnap: function(position, lineWidth) {\r\n      if (lineWidth % 2 === 0) {\r\n        // Closest pixel edge.\r\n        return Math.round(position);\r\n      } else {\r\n        // Closest pixel center.\r\n        return Math.floor(position) + 0.5;\r\n      }\r\n    },\r\n  };\r\n\r\n  /**\r\n   * Initialises a new <code>TimeSeries</code> with optional data options.\r\n   *\r\n   * Options are of the form (defaults shown):\r\n   *\r\n   * <pre>\r\n   * {\r\n   *   resetBounds: true,        // enables/disables automatic scaling of the y-axis\r\n   *   resetBoundsInterval: 3000 // the period between scaling calculations, in millis\r\n   * }\r\n   * </pre>\r\n   *\r\n   * Presentation options for TimeSeries are specified as an argument to <code>SmoothieChart.addTimeSeries</code>.\r\n   *\r\n   * @constructor\r\n   */\r\n  function TimeSeries(options) {\r\n    this.options = Util.extend({}, TimeSeries.defaultOptions, options);\r\n    this.disabled = false;\r\n    this.clear();\r\n  }\r\n\r\n  TimeSeries.defaultOptions = {\r\n    resetBoundsInterval: 3000,\r\n    resetBounds: true\r\n  };\r\n\r\n  /**\r\n   * Clears all data and state from this TimeSeries object.\r\n   */\r\n  TimeSeries.prototype.clear = function() {\r\n    this.data = [];\r\n    this.maxValue = Number.NaN; // The maximum value ever seen in this TimeSeries.\r\n    this.minValue = Number.NaN; // The minimum value ever seen in this TimeSeries.\r\n  };\r\n\r\n  /**\r\n   * Recalculate the min/max values for this <code>TimeSeries</code> object.\r\n   *\r\n   * This causes the graph to scale itself in the y-axis.\r\n   */\r\n  TimeSeries.prototype.resetBounds = function() {\r\n    if (this.data.length) {\r\n      // Walk through all data points, finding the min/max value\r\n      this.maxValue = this.data[0][1];\r\n      this.minValue = this.data[0][1];\r\n      for (var i = 1; i < this.data.length; i++) {\r\n        var value = this.data[i][1];\r\n        if (value > this.maxValue) {\r\n          this.maxValue = value;\r\n        }\r\n        if (value < this.minValue) {\r\n          this.minValue = value;\r\n        }\r\n      }\r\n    } else {\r\n      // No data exists, so set min/max to NaN\r\n      this.maxValue = Number.NaN;\r\n      this.minValue = Number.NaN;\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Adds a new data point to the <code>TimeSeries</code>, preserving chronological order.\r\n   *\r\n   * @param timestamp the position, in time, of this data point\r\n   * @param value the value of this data point\r\n   * @param sumRepeatedTimeStampValues if <code>timestamp</code> has an exact match in the series, this flag controls\r\n   * whether it is replaced, or the values summed (defaults to false.)\r\n   */\r\n  TimeSeries.prototype.append = function(timestamp, value, sumRepeatedTimeStampValues) {\r\n\t// Reject NaN\r\n\tif (isNaN(timestamp) || isNaN(value)){\r\n\t\treturn\r\n\t}  \r\n\r\n    var lastI = this.data.length - 1;\r\n    if (lastI >= 0) {\r\n      // Rewind until we find the place for the new data\r\n      var i = lastI;\r\n      while (true) {\r\n        var iThData = this.data[i];\r\n        if (timestamp >= iThData[0]) {\r\n          if (timestamp === iThData[0]) {\r\n            // Update existing values in the array\r\n            if (sumRepeatedTimeStampValues) {\r\n              // Sum this value into the existing 'bucket'\r\n              iThData[1] += value;\r\n              value = iThData[1];\r\n            } else {\r\n              // Replace the previous value\r\n              iThData[1] = value;\r\n            }\r\n          } else {\r\n            // Splice into the correct position to keep timestamps in order\r\n            this.data.splice(i + 1, 0, [timestamp, value]);\r\n          }\r\n\r\n          break;\r\n        }\r\n\r\n        i--;\r\n        if (i < 0) {\r\n          // This new item is the oldest data\r\n          this.data.splice(0, 0, [timestamp, value]);\r\n\r\n          break;\r\n        }\r\n      }\r\n    } else {\r\n      // It's the first element\r\n      this.data.push([timestamp, value]);\r\n    }\r\n\r\n    this.maxValue = isNaN(this.maxValue) ? value : Math.max(this.maxValue, value);\r\n    this.minValue = isNaN(this.minValue) ? value : Math.min(this.minValue, value);\r\n  };\r\n\r\n  TimeSeries.prototype.dropOldData = function(oldestValidTime, maxDataSetLength) {\r\n    // We must always keep one expired data point as we need this to draw the\r\n    // line that comes into the chart from the left, but any points prior to that can be removed.\r\n    var removeCount = 0;\r\n    while (this.data.length - removeCount >= maxDataSetLength && this.data[removeCount + 1][0] < oldestValidTime) {\r\n      removeCount++;\r\n    }\r\n    if (removeCount !== 0) {\r\n      this.data.splice(0, removeCount);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Initialises a new <code>SmoothieChart</code>.\r\n   *\r\n   * Options are optional, and should be of the form below. Just specify the values you\r\n   * need and the rest will be given sensible defaults as shown:\r\n   *\r\n   * <pre>\r\n   * {\r\n   *   minValue: undefined,                      // specify to clamp the lower y-axis to a given value\r\n   *   maxValue: undefined,                      // specify to clamp the upper y-axis to a given value\r\n   *   maxValueScale: 1,                         // allows proportional padding to be added above the chart. for 10% padding, specify 1.1.\r\n   *   minValueScale: 1,                         // allows proportional padding to be added below the chart. for 10% padding, specify 1.1.\r\n   *   yRangeFunction: undefined,                // function({min: , max: }) { return {min: , max: }; }\r\n   *   scaleSmoothing: 0.125,                    // controls the rate at which y-value zoom animation occurs\r\n   *   millisPerPixel: 20,                       // sets the speed at which the chart pans by\r\n   *   enableDpiScaling: true,                   // support rendering at different DPI depending on the device\r\n   *   yMinFormatter: function(min, precision) { // callback function that formats the min y value label\r\n   *     return parseFloat(min).toFixed(precision);\r\n   *   },\r\n   *   yMaxFormatter: function(max, precision) { // callback function that formats the max y value label\r\n   *     return parseFloat(max).toFixed(precision);\r\n   *   },\r\n   *   yIntermediateFormatter: function(intermediate, precision) { // callback function that formats the intermediate y value labels\r\n   *     return parseFloat(intermediate).toFixed(precision);\r\n   *   },\r\n   *   maxDataSetLength: 2,\r\n   *   interpolation: 'bezier'                   // one of 'bezier', 'linear', or 'step'\r\n   *   timestampFormatter: null,                 // optional function to format time stamps for bottom of chart\r\n   *                                             // you may use SmoothieChart.timeFormatter, or your own: function(date) { return ''; }\r\n   *   scrollBackwards: false,                   // reverse the scroll direction of the chart\r\n   *   horizontalLines: [],                      // [ { value: 0, color: '#ffffff', lineWidth: 1 } ]\r\n   *   grid:\r\n   *   {\r\n   *     fillStyle: '#000000',                   // the background colour of the chart\r\n   *     lineWidth: 1,                           // the pixel width of grid lines\r\n   *     strokeStyle: '#777777',                 // colour of grid lines\r\n   *     millisPerLine: 1000,                    // distance between vertical grid lines\r\n   *     verticalSections: 2,                    // number of vertical sections marked out by horizontal grid lines\r\n   *     borderVisible: true                     // whether the grid lines trace the border of the chart or not\r\n   *   },\r\n   *   labels\r\n   *   {\r\n   *     disabled: false,                        // enables/disables labels showing the min/max values\r\n   *     fillStyle: '#ffffff',                   // colour for text of labels,\r\n   *     fontSize: 15,\r\n   *     fontFamily: 'sans-serif',\r\n   *     precision: 2,\r\n   *     showIntermediateLabels: false,          // shows intermediate labels between min and max values along y axis\r\n   *     intermediateLabelSameAxis: true,\r\n   *   },\r\n   *   title\r\n   *   {\r\n   *     text: '',                               // the text to display on the left side of the chart\r\n   *     fillStyle: '#ffffff',                   // colour for text\r\n   *     fontSize: 15,\r\n   *     fontFamily: 'sans-serif',\r\n   *     verticalAlign: 'middle'                 // one of 'top', 'middle', or 'bottom'\r\n   *   },\r\n   *   tooltip: false                            // show tooltip when mouse is over the chart\r\n   *   tooltipLine: {                            // properties for a vertical line at the cursor position\r\n   *     lineWidth: 1,\r\n   *     strokeStyle: '#BBBBBB'\r\n   *   },\r\n   *   tooltipFormatter: SmoothieChart.tooltipFormatter, // formatter function for tooltip text\r\n   *   nonRealtimeData: false,                   // use time of latest data as current time\r\n   *   displayDataFromPercentile: 1,             // display not latest data, but data from the given percentile\r\n   *                                             // useful when trying to see old data saved by setting a high value for maxDataSetLength\r\n   *                                             // should be a value between 0 and 1\r\n   *   responsive: false,                        // whether the chart should adapt to the size of the canvas\r\n   *   limitFPS: 0                               // maximum frame rate the chart will render at, in FPS (zero means no limit)\r\n   * }\r\n   * </pre>\r\n   *\r\n   * @constructor\r\n   */\r\n  function SmoothieChart(options) {\r\n    this.options = Util.extend({}, SmoothieChart.defaultChartOptions, options);\r\n    this.seriesSet = [];\r\n    this.currentValueRange = 1;\r\n    this.currentVisMinValue = 0;\r\n    this.lastRenderTimeMillis = 0;\r\n    this.lastChartTimestamp = 0;\r\n\r\n    this.mousemove = this.mousemove.bind(this);\r\n    this.mouseout = this.mouseout.bind(this);\r\n  }\r\n\r\n  /** Formats the HTML string content of the tooltip. */\r\n  SmoothieChart.tooltipFormatter = function (timestamp, data) {\r\n      var timestampFormatter = this.options.timestampFormatter || SmoothieChart.timeFormatter,\r\n          // A dummy element to hold children. Maybe there's a better way.\r\n          elements = document.createElement('div'),\r\n          label;\r\n      elements.appendChild(document.createTextNode(\r\n        timestampFormatter(new Date(timestamp))\r\n      ));\r\n\r\n      for (var i = 0; i < data.length; ++i) {\r\n        label = data[i].series.options.tooltipLabel || ''\r\n        if (label !== ''){\r\n            label = label + ' ';\r\n        }\r\n        var dataEl = document.createElement('span');\r\n        dataEl.style.color = data[i].series.options.strokeStyle;\r\n        dataEl.appendChild(document.createTextNode(\r\n          label + this.options.yMaxFormatter(data[i].value, this.options.labels.precision)\r\n        ));\r\n        elements.appendChild(document.createElement('br'));\r\n        elements.appendChild(dataEl);\r\n      }\r\n\r\n      return elements.innerHTML;\r\n  };\r\n\r\n  SmoothieChart.defaultChartOptions = {\r\n    millisPerPixel: 20,\r\n    enableDpiScaling: true,\r\n    yMinFormatter: function(min, precision) {\r\n      return parseFloat(min).toFixed(precision);\r\n    },\r\n    yMaxFormatter: function(max, precision) {\r\n      return parseFloat(max).toFixed(precision);\r\n    },\r\n    yIntermediateFormatter: function(intermediate, precision) {\r\n      return parseFloat(intermediate).toFixed(precision);\r\n    },\r\n    maxValueScale: 1,\r\n    minValueScale: 1,\r\n    interpolation: 'bezier',\r\n    scaleSmoothing: 0.125,\r\n    maxDataSetLength: 2,\r\n    scrollBackwards: false,\r\n    displayDataFromPercentile: 1,\r\n    grid: {\r\n      fillStyle: '#000000',\r\n      strokeStyle: '#777777',\r\n      lineWidth: 2,\r\n      millisPerLine: 1000,\r\n      verticalSections: 2,\r\n      borderVisible: true\r\n    },\r\n    labels: {\r\n      fillStyle: '#ffffff',\r\n      disabled: false,\r\n      fontSize: 10,\r\n      fontFamily: 'monospace',\r\n      precision: 2,\r\n      showIntermediateLabels: false,\r\n      intermediateLabelSameAxis: true,\r\n    },\r\n    title: {\r\n      text: '',\r\n      fillStyle: '#ffffff',\r\n      fontSize: 15,\r\n      fontFamily: 'monospace',\r\n      verticalAlign: 'middle'\r\n    },\r\n    horizontalLines: [],\r\n    tooltip: false,\r\n    tooltipLine: {\r\n      lineWidth: 1,\r\n      strokeStyle: '#BBBBBB'\r\n    },\r\n    tooltipFormatter: SmoothieChart.tooltipFormatter,\r\n    nonRealtimeData: false,\r\n    responsive: false,\r\n    limitFPS: 0\r\n  };\r\n\r\n  // Based on http://inspirit.github.com/jsfeat/js/compatibility.js\r\n  SmoothieChart.AnimateCompatibility = (function() {\r\n    var requestAnimationFrame = function(callback, element) {\r\n          var requestAnimationFrame =\r\n            window.requestAnimationFrame        ||\r\n            window.webkitRequestAnimationFrame  ||\r\n            window.mozRequestAnimationFrame     ||\r\n            window.oRequestAnimationFrame       ||\r\n            window.msRequestAnimationFrame      ||\r\n            function(callback) {\r\n              return window.setTimeout(function() {\r\n                callback(Date.now());\r\n              }, 16);\r\n            };\r\n          return requestAnimationFrame.call(window, callback, element);\r\n        },\r\n        cancelAnimationFrame = function(id) {\r\n          var cancelAnimationFrame =\r\n            window.cancelAnimationFrame ||\r\n            function(id) {\r\n              clearTimeout(id);\r\n            };\r\n          return cancelAnimationFrame.call(window, id);\r\n        };\r\n\r\n    return {\r\n      requestAnimationFrame: requestAnimationFrame,\r\n      cancelAnimationFrame: cancelAnimationFrame\r\n    };\r\n  })();\r\n\r\n  SmoothieChart.defaultSeriesPresentationOptions = {\r\n    lineWidth: 1,\r\n    strokeStyle: '#ffffff'\r\n  };\r\n\r\n  /**\r\n   * Adds a <code>TimeSeries</code> to this chart, with optional presentation options.\r\n   *\r\n   * Presentation options should be of the form (defaults shown):\r\n   *\r\n   * <pre>\r\n   * {\r\n   *   lineWidth: 1,\r\n   *   strokeStyle: '#ffffff',\r\n   *   fillStyle: undefined,\r\n   *   interpolation: undefined;\r\n   *   tooltipLabel: undefined\r\n   * }\r\n   * </pre>\r\n   */\r\n  SmoothieChart.prototype.addTimeSeries = function(timeSeries, options) {\r\n    this.seriesSet.push({timeSeries: timeSeries, options: Util.extend({}, SmoothieChart.defaultSeriesPresentationOptions, options)});\r\n    if (timeSeries.options.resetBounds && timeSeries.options.resetBoundsInterval > 0) {\r\n      timeSeries.resetBoundsTimerId = setInterval(\r\n        function() {\r\n          timeSeries.resetBounds();\r\n        },\r\n        timeSeries.options.resetBoundsInterval\r\n      );\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Removes the specified <code>TimeSeries</code> from the chart.\r\n   */\r\n  SmoothieChart.prototype.removeTimeSeries = function(timeSeries) {\r\n    // Find the correct timeseries to remove, and remove it\r\n    var numSeries = this.seriesSet.length;\r\n    for (var i = 0; i < numSeries; i++) {\r\n      if (this.seriesSet[i].timeSeries === timeSeries) {\r\n        this.seriesSet.splice(i, 1);\r\n        break;\r\n      }\r\n    }\r\n    // If a timer was operating for that timeseries, remove it\r\n    if (timeSeries.resetBoundsTimerId) {\r\n      // Stop resetting the bounds, if we were\r\n      clearInterval(timeSeries.resetBoundsTimerId);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Gets render options for the specified <code>TimeSeries</code>.\r\n   *\r\n   * As you may use a single <code>TimeSeries</code> in multiple charts with different formatting in each usage,\r\n   * these settings are stored in the chart.\r\n   */\r\n  SmoothieChart.prototype.getTimeSeriesOptions = function(timeSeries) {\r\n    // Find the correct timeseries to remove, and remove it\r\n    var numSeries = this.seriesSet.length;\r\n    for (var i = 0; i < numSeries; i++) {\r\n      if (this.seriesSet[i].timeSeries === timeSeries) {\r\n        return this.seriesSet[i].options;\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Brings the specified <code>TimeSeries</code> to the top of the chart. It will be rendered last.\r\n   */\r\n  SmoothieChart.prototype.bringToFront = function(timeSeries) {\r\n    // Find the correct timeseries to remove, and remove it\r\n    var numSeries = this.seriesSet.length;\r\n    for (var i = 0; i < numSeries; i++) {\r\n      if (this.seriesSet[i].timeSeries === timeSeries) {\r\n        var set = this.seriesSet.splice(i, 1);\r\n        this.seriesSet.push(set[0]);\r\n        break;\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Instructs the <code>SmoothieChart</code> to start rendering to the provided canvas, with specified delay.\r\n   *\r\n   * @param canvas the target canvas element\r\n   * @param delayMillis an amount of time to wait before a data point is shown. This can prevent the end of the series\r\n   * from appearing on screen, with new values flashing into view, at the expense of some latency.\r\n   */\r\n  SmoothieChart.prototype.streamTo = function(canvas, delayMillis) {\r\n    this.canvas = canvas;\r\n\r\n    this.clientWidth = parseInt(this.canvas.getAttribute('width'));\r\n    this.clientHeight = parseInt(this.canvas.getAttribute('height'));\r\n\r\n    this.delay = delayMillis;\r\n    this.start();\r\n  };\r\n\r\n  SmoothieChart.prototype.getTooltipEl = function () {\r\n    // Create the tool tip element lazily\r\n    if (!this.tooltipEl) {\r\n      this.tooltipEl = document.createElement('div');\r\n      this.tooltipEl.className = 'smoothie-chart-tooltip';\r\n      this.tooltipEl.style.pointerEvents = 'none';\r\n      this.tooltipEl.style.position = 'absolute';\r\n      this.tooltipEl.style.display = 'none';\r\n      document.body.appendChild(this.tooltipEl);\r\n    }\r\n    return this.tooltipEl;\r\n  };\r\n\r\n  SmoothieChart.prototype.updateTooltip = function () {\r\n    if(!this.options.tooltip){\r\n     return; \r\n    }\r\n    var el = this.getTooltipEl();\r\n\r\n    if (!this.mouseover || !this.options.tooltip) {\r\n      el.style.display = 'none';\r\n      return;\r\n    }\r\n\r\n    var time = this.lastChartTimestamp;\r\n\r\n    // x pixel to time\r\n    var t = this.options.scrollBackwards\r\n      ? time - this.mouseX * this.options.millisPerPixel\r\n      : time - (this.clientWidth - this.mouseX) * this.options.millisPerPixel;\r\n\r\n    var data = [];\r\n\r\n     // For each data set...\r\n    for (var d = 0; d < this.seriesSet.length; d++) {\r\n      var timeSeries = this.seriesSet[d].timeSeries;\r\n      if (timeSeries.disabled) {\r\n          continue;\r\n      }\r\n\r\n      // find datapoint closest to time 't'\r\n      var closeIdx = Util.binarySearch(timeSeries.data, t);\r\n      if (closeIdx > 0 && closeIdx < timeSeries.data.length) {\r\n        data.push({ series: this.seriesSet[d], index: closeIdx, value: timeSeries.data[closeIdx][1] });\r\n      }\r\n    }\r\n\r\n    if (data.length) {\r\n      // TODO make `tooltipFormatter` return element(s) instead of an HTML string so it's harder for users\r\n      // to introduce an XSS. This would be a breaking change.\r\n      el.innerHTML = this.options.tooltipFormatter.call(this, t, data);\r\n      el.style.display = 'block';\r\n    } else {\r\n      el.style.display = 'none';\r\n    }\r\n  };\r\n\r\n  SmoothieChart.prototype.mousemove = function (evt) {\r\n    this.mouseover = true;\r\n    this.mouseX = evt.offsetX;\r\n    this.mouseY = evt.offsetY;\r\n    this.mousePageX = evt.pageX;\r\n    this.mousePageY = evt.pageY;\r\n    if(!this.options.tooltip){\r\n     return; \r\n    }\r\n    var el = this.getTooltipEl();\r\n    el.style.top = Math.round(this.mousePageY) + 'px';\r\n    el.style.left = Math.round(this.mousePageX) + 'px';\r\n    this.updateTooltip();\r\n  };\r\n\r\n  SmoothieChart.prototype.mouseout = function () {\r\n    this.mouseover = false;\r\n    this.mouseX = this.mouseY = -1;\r\n    if (this.tooltipEl)\r\n      this.tooltipEl.style.display = 'none';\r\n  };\r\n\r\n  /**\r\n   * Make sure the canvas has the optimal resolution for the device's pixel ratio.\r\n   */\r\n  SmoothieChart.prototype.resize = function () {\r\n    var dpr = !this.options.enableDpiScaling || !window ? 1 : window.devicePixelRatio,\r\n        width, height;\r\n    if (this.options.responsive) {\r\n      // Newer behaviour: Use the canvas's size in the layout, and set the internal\r\n      // resolution according to that size and the device pixel ratio (eg: high DPI)\r\n      width = this.canvas.offsetWidth;\r\n      height = this.canvas.offsetHeight;\r\n\r\n      if (width !== this.lastWidth) {\r\n        this.lastWidth = width;\r\n        this.canvas.setAttribute('width', (Math.floor(width * dpr)).toString());\r\n        this.canvas.getContext('2d').scale(dpr, dpr);\r\n      }\r\n      if (height !== this.lastHeight) {\r\n        this.lastHeight = height;\r\n        this.canvas.setAttribute('height', (Math.floor(height * dpr)).toString());\r\n        this.canvas.getContext('2d').scale(dpr, dpr);\r\n      }\r\n\r\n      this.clientWidth = width;\r\n      this.clientHeight = height;\r\n    } else {\r\n      width = parseInt(this.canvas.getAttribute('width'));\r\n      height = parseInt(this.canvas.getAttribute('height'));\r\n\r\n      if (dpr !== 1) {\r\n        // Older behaviour: use the canvas's inner dimensions and scale the element's size\r\n        // according to that size and the device pixel ratio (eg: high DPI)\r\n\r\n        if (Math.floor(this.clientWidth * dpr) !== width) {\r\n          this.canvas.setAttribute('width', (Math.floor(width * dpr)).toString());\r\n          this.canvas.style.width = width + 'px';\r\n          this.clientWidth = width;\r\n          this.canvas.getContext('2d').scale(dpr, dpr);\r\n        }\r\n\r\n        if (Math.floor(this.clientHeight * dpr) !== height) {\r\n          this.canvas.setAttribute('height', (Math.floor(height * dpr)).toString());\r\n          this.canvas.style.height = height + 'px';\r\n          this.clientHeight = height;\r\n          this.canvas.getContext('2d').scale(dpr, dpr);\r\n        }\r\n      } else {\r\n        this.clientWidth = width;\r\n        this.clientHeight = height;\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Starts the animation of this chart.\r\n   */\r\n  SmoothieChart.prototype.start = function() {\r\n    if (this.frame) {\r\n      // We're already running, so just return\r\n      return;\r\n    }\r\n\r\n    this.canvas.addEventListener('mousemove', this.mousemove);\r\n    this.canvas.addEventListener('mouseout', this.mouseout);\r\n\r\n    // Renders a frame, and queues the next frame for later rendering\r\n    var animate = function() {\r\n      this.frame = SmoothieChart.AnimateCompatibility.requestAnimationFrame(function() {\r\n        if(this.options.nonRealtimeData){\r\n           var dateZero = new Date(0);\r\n           // find the data point with the latest timestamp\r\n           var maxTimeStamp = this.seriesSet.reduce(function(max, series){\r\n             var dataSet = series.timeSeries.data;\r\n             var indexToCheck = Math.round(this.options.displayDataFromPercentile * dataSet.length) - 1;\r\n             indexToCheck = indexToCheck >= 0 ? indexToCheck : 0;\r\n             indexToCheck = indexToCheck <= dataSet.length -1 ? indexToCheck : dataSet.length -1;\r\n             if(dataSet && dataSet.length > 0)\r\n             {\r\n              // timestamp corresponds to element 0 of the data point\r\n              var lastDataTimeStamp = dataSet[indexToCheck][0];\r\n              max = max > lastDataTimeStamp ? max : lastDataTimeStamp;\r\n             }\r\n             return max;\r\n          }.bind(this), dateZero);\r\n          // use the max timestamp as current time\r\n          this.render(this.canvas, maxTimeStamp > dateZero ? maxTimeStamp : null);\r\n        } else {\r\n          this.render();\r\n        }\r\n        animate();\r\n      }.bind(this));\r\n    }.bind(this);\r\n\r\n    animate();\r\n  };\r\n\r\n  /**\r\n   * Stops the animation of this chart.\r\n   */\r\n  SmoothieChart.prototype.stop = function() {\r\n    if (this.frame) {\r\n      SmoothieChart.AnimateCompatibility.cancelAnimationFrame(this.frame);\r\n      delete this.frame;\r\n      this.canvas.removeEventListener('mousemove', this.mousemove);\r\n      this.canvas.removeEventListener('mouseout', this.mouseout);\r\n    }\r\n  };\r\n\r\n  SmoothieChart.prototype.updateValueRange = function() {\r\n    // Calculate the current scale of the chart, from all time series.\r\n    var chartOptions = this.options,\r\n        chartMaxValue = Number.NaN,\r\n        chartMinValue = Number.NaN;\r\n\r\n    for (var d = 0; d < this.seriesSet.length; d++) {\r\n      // TODO(ndunn): We could calculate / track these values as they stream in.\r\n      var timeSeries = this.seriesSet[d].timeSeries;\r\n      if (timeSeries.disabled) {\r\n          continue;\r\n      }\r\n\r\n      if (!isNaN(timeSeries.maxValue)) {\r\n        chartMaxValue = !isNaN(chartMaxValue) ? Math.max(chartMaxValue, timeSeries.maxValue) : timeSeries.maxValue;\r\n      }\r\n\r\n      if (!isNaN(timeSeries.minValue)) {\r\n        chartMinValue = !isNaN(chartMinValue) ? Math.min(chartMinValue, timeSeries.minValue) : timeSeries.minValue;\r\n      }\r\n    }\r\n\r\n    // Scale the chartMaxValue to add padding at the top if required\r\n    if (chartOptions.maxValue != null) {\r\n      chartMaxValue = chartOptions.maxValue;\r\n    } else {\r\n      chartMaxValue *= chartOptions.maxValueScale;\r\n    }\r\n\r\n    // Set the minimum if we've specified one\r\n    if (chartOptions.minValue != null) {\r\n      chartMinValue = chartOptions.minValue;\r\n    } else {\r\n      chartMinValue -= Math.abs(chartMinValue * chartOptions.minValueScale - chartMinValue);\r\n    }\r\n\r\n    // If a custom range function is set, call it\r\n    if (this.options.yRangeFunction) {\r\n      var range = this.options.yRangeFunction({min: chartMinValue, max: chartMaxValue});\r\n      chartMinValue = range.min;\r\n      chartMaxValue = range.max;\r\n    }\r\n\r\n    if (!isNaN(chartMaxValue) && !isNaN(chartMinValue)) {\r\n      var targetValueRange = chartMaxValue - chartMinValue;\r\n      var valueRangeDiff = (targetValueRange - this.currentValueRange);\r\n      var minValueDiff = (chartMinValue - this.currentVisMinValue);\r\n      this.isAnimatingScale = Math.abs(valueRangeDiff) > 0.1 || Math.abs(minValueDiff) > 0.1;\r\n      this.currentValueRange += chartOptions.scaleSmoothing * valueRangeDiff;\r\n      this.currentVisMinValue += chartOptions.scaleSmoothing * minValueDiff;\r\n    }\r\n\r\n    this.valueRange = { min: chartMinValue, max: chartMaxValue };\r\n  };\r\n\r\n  SmoothieChart.prototype.render = function(canvas, time) {\r\n    var nowMillis = Date.now();\r\n\r\n    // Respect any frame rate limit.\r\n    if (this.options.limitFPS > 0 && nowMillis - this.lastRenderTimeMillis < (1000/this.options.limitFPS))\r\n      return;\r\n\r\n    time = (time || nowMillis) - (this.delay || 0);\r\n\r\n    // Round time down to pixel granularity, so motion appears smoother.\r\n    time -= time % this.options.millisPerPixel;\r\n\r\n    if (!this.isAnimatingScale) {\r\n      // We're not animating. We can use the last render time and the scroll speed to work out whether\r\n      // we actually need to paint anything yet. If not, we can return immediately.\r\n      var sameTime = this.lastChartTimestamp === time;\r\n      if (sameTime) {\r\n        // Render at least every 1/6th of a second. The canvas may be resized, which there is\r\n        // no reliable way to detect.\r\n        var needToRenderInCaseCanvasResized = nowMillis - this.lastRenderTimeMillis > 1000/6;\r\n        if (!needToRenderInCaseCanvasResized) {\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    this.lastRenderTimeMillis = nowMillis;\r\n    this.lastChartTimestamp = time;\r\n\r\n    this.resize();\r\n\r\n    canvas = canvas || this.canvas;\r\n    var context = canvas.getContext('2d'),\r\n        chartOptions = this.options,\r\n        // Using `this.clientWidth` instead of `canvas.clientWidth` because the latter is slow.\r\n        dimensions = { top: 0, left: 0, width: this.clientWidth, height: this.clientHeight },\r\n        // Calculate the threshold time for the oldest data points.\r\n        oldestValidTime = time - (dimensions.width * chartOptions.millisPerPixel),\r\n        valueToYPosition = function(value, lineWidth) {\r\n          var offset = value - this.currentVisMinValue,\r\n              unsnapped = this.currentValueRange === 0\r\n                ? dimensions.height\r\n                : dimensions.height * (1 - offset / this.currentValueRange);\r\n          return Util.pixelSnap(unsnapped, lineWidth);\r\n        }.bind(this),\r\n        timeToXPosition = function(t, lineWidth) {\r\n          var unsnapped = chartOptions.scrollBackwards\r\n            ? (time - t) / chartOptions.millisPerPixel\r\n            : dimensions.width - ((time - t) / chartOptions.millisPerPixel);\r\n          return Util.pixelSnap(unsnapped, lineWidth);\r\n        };\r\n\r\n    this.updateValueRange();\r\n\r\n    context.font = chartOptions.labels.fontSize + 'px ' + chartOptions.labels.fontFamily;\r\n\r\n    // Save the state of the canvas context, any transformations applied in this method\r\n    // will get removed from the stack at the end of this method when .restore() is called.\r\n    context.save();\r\n\r\n    // Move the origin.\r\n    context.translate(dimensions.left, dimensions.top);\r\n\r\n    // Create a clipped rectangle - anything we draw will be constrained to this rectangle.\r\n    // This prevents the occasional pixels from curves near the edges overrunning and creating\r\n    // screen cheese (that phrase should need no explanation).\r\n    context.beginPath();\r\n    context.rect(0, 0, dimensions.width, dimensions.height);\r\n    context.clip();\r\n\r\n    // Clear the working area.\r\n    context.save();\r\n    context.fillStyle = chartOptions.grid.fillStyle;\r\n    context.clearRect(0, 0, dimensions.width, dimensions.height);\r\n    context.fillRect(0, 0, dimensions.width, dimensions.height);\r\n    context.restore();\r\n\r\n    // Grid lines...\r\n    context.save();\r\n    context.lineWidth = chartOptions.grid.lineWidth;\r\n    context.strokeStyle = chartOptions.grid.strokeStyle;\r\n    // Vertical (time) dividers.\r\n    if (chartOptions.grid.millisPerLine > 0) {\r\n      context.beginPath();\r\n      for (var t = time - (time % chartOptions.grid.millisPerLine);\r\n           t >= oldestValidTime;\r\n           t -= chartOptions.grid.millisPerLine) {\r\n        var gx = timeToXPosition(t, chartOptions.grid.lineWidth);\r\n        context.moveTo(gx, 0);\r\n        context.lineTo(gx, dimensions.height);\r\n      }\r\n      context.stroke();\r\n      context.closePath();\r\n    }\r\n\r\n    // Horizontal (value) dividers.\r\n    for (var v = 1; v < chartOptions.grid.verticalSections; v++) {\r\n      var gy = Util.pixelSnap(v * dimensions.height / chartOptions.grid.verticalSections, chartOptions.grid.lineWidth);\r\n      context.beginPath();\r\n      context.moveTo(0, gy);\r\n      context.lineTo(dimensions.width, gy);\r\n      context.stroke();\r\n      context.closePath();\r\n    }\r\n    // Bounding rectangle.\r\n    if (chartOptions.grid.borderVisible) {\r\n      context.beginPath();\r\n      context.strokeRect(0, 0, dimensions.width, dimensions.height);\r\n      context.closePath();\r\n    }\r\n    context.restore();\r\n\r\n    // Draw any horizontal lines...\r\n    if (chartOptions.horizontalLines && chartOptions.horizontalLines.length) {\r\n      for (var hl = 0; hl < chartOptions.horizontalLines.length; hl++) {\r\n        var line = chartOptions.horizontalLines[hl],\r\n            lineWidth = line.lineWidth || 1,\r\n            hly = valueToYPosition(line.value, lineWidth);\r\n        context.strokeStyle = line.color || '#ffffff';\r\n        context.lineWidth = lineWidth;\r\n        context.beginPath();\r\n        context.moveTo(0, hly);\r\n        context.lineTo(dimensions.width, hly);\r\n        context.stroke();\r\n        context.closePath();\r\n      }\r\n    }\r\n\r\n    // For each data set...\r\n    for (var d = 0; d < this.seriesSet.length; d++) {\r\n      var timeSeries = this.seriesSet[d].timeSeries,\r\n          dataSet = timeSeries.data;\r\n\r\n      // Delete old data that's moved off the left of the chart.\r\n      timeSeries.dropOldData(oldestValidTime, chartOptions.maxDataSetLength);\r\n      if (dataSet.length <= 1 || timeSeries.disabled) {\r\n          continue;\r\n      }\r\n      context.save();\r\n\r\n      var seriesOptions = this.seriesSet[d].options,\r\n          // Keep in mind that `context.lineWidth = 0` doesn't actually set it to `0`.\r\n          drawStroke = seriesOptions.strokeStyle && seriesOptions.strokeStyle !== 'none',\r\n          lineWidthMaybeZero = drawStroke ? seriesOptions.lineWidth : 0;\r\n\r\n      // Draw the line...\r\n      context.beginPath();\r\n      // Retain lastX, lastY for calculating the control points of bezier curves.\r\n      var firstX = timeToXPosition(dataSet[0][0], lineWidthMaybeZero),\r\n        firstY = valueToYPosition(dataSet[0][1], lineWidthMaybeZero),\r\n        lastX = firstX,\r\n        lastY = firstY,\r\n        draw;\r\n      context.moveTo(firstX, firstY);\r\n      switch (seriesOptions.interpolation || chartOptions.interpolation) {\r\n        case \"linear\":\r\n        case \"line\": {\r\n          draw = function(x, y, lastX, lastY) {\r\n            context.lineTo(x,y);\r\n          }\r\n          break;\r\n        }\r\n        case \"bezier\":\r\n        default: {\r\n          // Great explanation of Bezier curves: http://en.wikipedia.org/wiki/Bezier_curve#Quadratic_curves\r\n          //\r\n          // Assuming A was the last point in the line plotted and B is the new point,\r\n          // we draw a curve with control points P and Q as below.\r\n          //\r\n          // A---P\r\n          //     |\r\n          //     |\r\n          //     |\r\n          //     Q---B\r\n          //\r\n          // Importantly, A and P are at the same y coordinate, as are B and Q. This is\r\n          // so adjacent curves appear to flow as one.\r\n          //\r\n          draw = function(x, y, lastX, lastY) {\r\n            context.bezierCurveTo( // startPoint (A) is implicit from last iteration of loop\r\n              Math.round((lastX + x) / 2), lastY, // controlPoint1 (P)\r\n              Math.round((lastX + x)) / 2, y, // controlPoint2 (Q)\r\n              x, y); // endPoint (B)\r\n          }\r\n          break;\r\n        }\r\n        case \"step\": {\r\n          draw = function(x, y, lastX, lastY) {\r\n            context.lineTo(x,lastY);\r\n            context.lineTo(x,y);\r\n          }\r\n          break;\r\n        }\r\n      }\r\n\r\n      for (var i = 1; i < dataSet.length; i++) {\r\n        var iThData = dataSet[i],\r\n            x = timeToXPosition(iThData[0], lineWidthMaybeZero),\r\n            y = valueToYPosition(iThData[1], lineWidthMaybeZero);\r\n        draw(x, y, lastX, lastY);\r\n        lastX = x; lastY = y;\r\n      }\r\n\r\n      if (drawStroke) {\r\n        context.lineWidth = seriesOptions.lineWidth;\r\n        context.strokeStyle = seriesOptions.strokeStyle;\r\n        context.stroke();\r\n      }\r\n\r\n      if (seriesOptions.fillStyle) {\r\n        // Close up the fill region.\r\n        context.lineTo(lastX, dimensions.height + lineWidthMaybeZero + 1);\r\n        context.lineTo(firstX, dimensions.height + lineWidthMaybeZero + 1);\r\n\r\n        context.fillStyle = seriesOptions.fillStyle;\r\n        context.fill();\r\n      }\r\n\r\n      context.restore();\r\n    }\r\n\r\n    if (chartOptions.tooltip && this.mouseX >= 0) {\r\n      // Draw vertical bar to show tooltip position\r\n      context.lineWidth = chartOptions.tooltipLine.lineWidth;\r\n      context.strokeStyle = chartOptions.tooltipLine.strokeStyle;\r\n      context.beginPath();\r\n      context.moveTo(this.mouseX, 0);\r\n      context.lineTo(this.mouseX, dimensions.height);\r\n      context.closePath();\r\n      context.stroke();\r\n    }\r\n    this.updateTooltip();\r\n\r\n    var labelsOptions = chartOptions.labels;\r\n    // Draw the axis values on the chart.\r\n    if (!labelsOptions.disabled && !isNaN(this.valueRange.min) && !isNaN(this.valueRange.max)) {\r\n      var maxValueString = chartOptions.yMaxFormatter(this.valueRange.max, labelsOptions.precision),\r\n          minValueString = chartOptions.yMinFormatter(this.valueRange.min, labelsOptions.precision),\r\n          maxLabelPos = chartOptions.scrollBackwards ? 0 : dimensions.width - context.measureText(maxValueString).width - 2,\r\n          minLabelPos = chartOptions.scrollBackwards ? 0 : dimensions.width - context.measureText(minValueString).width - 2;\r\n      context.fillStyle = labelsOptions.fillStyle;\r\n      context.fillText(maxValueString, maxLabelPos, labelsOptions.fontSize);\r\n      context.fillText(minValueString, minLabelPos, dimensions.height - 2);\r\n    }\r\n\r\n    // Display intermediate y axis labels along y-axis to the left of the chart\r\n    if ( labelsOptions.showIntermediateLabels\r\n          && !isNaN(this.valueRange.min) && !isNaN(this.valueRange.max)\r\n          && chartOptions.grid.verticalSections > 0) {\r\n      // show a label above every vertical section divider\r\n      var step = (this.valueRange.max - this.valueRange.min) / chartOptions.grid.verticalSections;\r\n      var stepPixels = dimensions.height / chartOptions.grid.verticalSections;\r\n      for (var v = 1; v < chartOptions.grid.verticalSections; v++) {\r\n        var gy = dimensions.height - Math.round(v * stepPixels),\r\n            yValue = chartOptions.yIntermediateFormatter(this.valueRange.min + (v * step), labelsOptions.precision),\r\n            //left of right axis?\r\n            intermediateLabelPos =\r\n              labelsOptions.intermediateLabelSameAxis\r\n              ? (chartOptions.scrollBackwards ? 0 : dimensions.width - context.measureText(yValue).width - 2)\r\n              : (chartOptions.scrollBackwards ? dimensions.width - context.measureText(yValue).width - 2 : 0);\r\n\r\n        context.fillText(yValue, intermediateLabelPos, gy - chartOptions.grid.lineWidth);\r\n      }\r\n    }\r\n\r\n    // Display timestamps along x-axis at the bottom of the chart.\r\n    if (chartOptions.timestampFormatter && chartOptions.grid.millisPerLine > 0) {\r\n      var textUntilX = chartOptions.scrollBackwards\r\n        ? context.measureText(minValueString).width\r\n        : dimensions.width - context.measureText(minValueString).width + 4;\r\n      for (var t = time - (time % chartOptions.grid.millisPerLine);\r\n           t >= oldestValidTime;\r\n           t -= chartOptions.grid.millisPerLine) {\r\n        var gx = timeToXPosition(t, 0);\r\n        // Only draw the timestamp if it won't overlap with the previously drawn one.\r\n        if ((!chartOptions.scrollBackwards && gx < textUntilX) || (chartOptions.scrollBackwards && gx > textUntilX))  {\r\n          // Formats the timestamp based on user specified formatting function\r\n          // SmoothieChart.timeFormatter function above is one such formatting option\r\n          var tx = new Date(t),\r\n            ts = chartOptions.timestampFormatter(tx),\r\n            tsWidth = context.measureText(ts).width;\r\n\r\n          textUntilX = chartOptions.scrollBackwards\r\n            ? gx + tsWidth + 2\r\n            : gx - tsWidth - 2;\r\n\r\n          context.fillStyle = chartOptions.labels.fillStyle;\r\n          if(chartOptions.scrollBackwards) {\r\n            context.fillText(ts, gx, dimensions.height - 2);\r\n          } else {\r\n            context.fillText(ts, gx - tsWidth, dimensions.height - 2);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // Display title.\r\n    if (chartOptions.title.text !== '') {\r\n      context.font = chartOptions.title.fontSize + 'px ' + chartOptions.title.fontFamily;\r\n      var titleXPos = chartOptions.scrollBackwards ? dimensions.width - context.measureText(chartOptions.title.text).width - 2 : 2;\r\n      if (chartOptions.title.verticalAlign == 'bottom') {\r\n        context.textBaseline = 'bottom';\r\n        var titleYPos = dimensions.height;\r\n      } else if (chartOptions.title.verticalAlign == 'middle') {\r\n        context.textBaseline = 'middle';\r\n        var titleYPos = dimensions.height / 2;\r\n      } else {\r\n        context.textBaseline = 'top';\r\n        var titleYPos = 0;\r\n      }\r\n      context.fillStyle = chartOptions.title.fillStyle;\r\n      context.fillText(chartOptions.title.text, titleXPos, titleYPos);\r\n    }\r\n\r\n    context.restore(); // See .save() above.\r\n  };\r\n\r\n  // Sample timestamp formatting function\r\n  SmoothieChart.timeFormatter = function(date) {\r\n    function pad2(number) { return (number < 10 ? '0' : '') + number }\r\n    return pad2(date.getHours()) + ':' + pad2(date.getMinutes()) + ':' + pad2(date.getSeconds());\r\n  };\r\n\r\n  exports.TimeSeries = TimeSeries;\r\n  exports.SmoothieChart = SmoothieChart;\r\n\r\n})(typeof exports === 'undefined' ? this : exports);\r\n\r\n"], "mappings": ";;;;;;AAAA;AAAA;AAAC,KAAC,SAASA,UAAS;AAgHlB,WAAK,MAAM,KAAK,OAAO,WAAW;AAAE,gBAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,MAAG;AAEjE,UAAI,OAAO;AAAA,QACT,QAAQ,WAAW;AACjB,oBAAU,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC;AAChC,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KACtC;AACE,qBAAS,OAAO,UAAU,CAAC,GAC3B;AACE,kBAAI,UAAU,CAAC,EAAE,eAAe,GAAG,GACnC;AACE,oBAAI,OAAO,UAAU,CAAC,EAAE,GAAG,MAAO,UAAU;AAC1C,sBAAI,UAAU,CAAC,EAAE,GAAG,aAAa,OAAO;AACtC,8BAAU,CAAC,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,GAAG;AAAA,kBACtC,OAAO;AACL,8BAAU,CAAC,EAAE,GAAG,IAAI,KAAK,OAAO,UAAU,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,EAAE,GAAG,CAAC;AAAA,kBACtE;AAAA,gBACF,OAAO;AACL,4BAAU,CAAC,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,GAAG;AAAA,gBACtC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,iBAAO,UAAU,CAAC;AAAA,QACpB;AAAA,QACA,cAAc,SAAS,MAAM,OAAO;AAClC,cAAI,MAAM,GACN,OAAO,KAAK;AAChB,iBAAO,MAAM,MAAM;AACjB,gBAAI,MAAO,MAAM,QAAS;AAC1B,gBAAI,QAAQ,KAAK,GAAG,EAAE,CAAC;AACrB,qBAAO;AAAA;AAEP,oBAAM,MAAM;AAAA,UAChB;AACA,iBAAO;AAAA,QACT;AAAA;AAAA,QAEA,WAAW,SAAS,UAAU,WAAW;AACvC,cAAI,YAAY,MAAM,GAAG;AAEvB,mBAAO,KAAK,MAAM,QAAQ;AAAA,UAC5B,OAAO;AAEL,mBAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAkBA,eAAS,WAAW,SAAS;AAC3B,aAAK,UAAU,KAAK,OAAO,CAAC,GAAG,WAAW,gBAAgB,OAAO;AACjE,aAAK,WAAW;AAChB,aAAK,MAAM;AAAA,MACb;AAEA,iBAAW,iBAAiB;AAAA,QAC1B,qBAAqB;AAAA,QACrB,aAAa;AAAA,MACf;AAKA,iBAAW,UAAU,QAAQ,WAAW;AACtC,aAAK,OAAO,CAAC;AACb,aAAK,WAAW,OAAO;AACvB,aAAK,WAAW,OAAO;AAAA,MACzB;AAOA,iBAAW,UAAU,cAAc,WAAW;AAC5C,YAAI,KAAK,KAAK,QAAQ;AAEpB,eAAK,WAAW,KAAK,KAAK,CAAC,EAAE,CAAC;AAC9B,eAAK,WAAW,KAAK,KAAK,CAAC,EAAE,CAAC;AAC9B,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACzC,gBAAI,QAAQ,KAAK,KAAK,CAAC,EAAE,CAAC;AAC1B,gBAAI,QAAQ,KAAK,UAAU;AACzB,mBAAK,WAAW;AAAA,YAClB;AACA,gBAAI,QAAQ,KAAK,UAAU;AACzB,mBAAK,WAAW;AAAA,YAClB;AAAA,UACF;AAAA,QACF,OAAO;AAEL,eAAK,WAAW,OAAO;AACvB,eAAK,WAAW,OAAO;AAAA,QACzB;AAAA,MACF;AAUA,iBAAW,UAAU,SAAS,SAAS,WAAW,OAAO,4BAA4B;AAEtF,YAAI,MAAM,SAAS,KAAK,MAAM,KAAK,GAAE;AACpC;AAAA,QACD;AAEG,YAAI,QAAQ,KAAK,KAAK,SAAS;AAC/B,YAAI,SAAS,GAAG;AAEd,cAAI,IAAI;AACR,iBAAO,MAAM;AACX,gBAAI,UAAU,KAAK,KAAK,CAAC;AACzB,gBAAI,aAAa,QAAQ,CAAC,GAAG;AAC3B,kBAAI,cAAc,QAAQ,CAAC,GAAG;AAE5B,oBAAI,4BAA4B;AAE9B,0BAAQ,CAAC,KAAK;AACd,0BAAQ,QAAQ,CAAC;AAAA,gBACnB,OAAO;AAEL,0BAAQ,CAAC,IAAI;AAAA,gBACf;AAAA,cACF,OAAO;AAEL,qBAAK,KAAK,OAAO,IAAI,GAAG,GAAG,CAAC,WAAW,KAAK,CAAC;AAAA,cAC/C;AAEA;AAAA,YACF;AAEA;AACA,gBAAI,IAAI,GAAG;AAET,mBAAK,KAAK,OAAO,GAAG,GAAG,CAAC,WAAW,KAAK,CAAC;AAEzC;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AAEL,eAAK,KAAK,KAAK,CAAC,WAAW,KAAK,CAAC;AAAA,QACnC;AAEA,aAAK,WAAW,MAAM,KAAK,QAAQ,IAAI,QAAQ,KAAK,IAAI,KAAK,UAAU,KAAK;AAC5E,aAAK,WAAW,MAAM,KAAK,QAAQ,IAAI,QAAQ,KAAK,IAAI,KAAK,UAAU,KAAK;AAAA,MAC9E;AAEA,iBAAW,UAAU,cAAc,SAAS,iBAAiB,kBAAkB;AAG7E,YAAI,cAAc;AAClB,eAAO,KAAK,KAAK,SAAS,eAAe,oBAAoB,KAAK,KAAK,cAAc,CAAC,EAAE,CAAC,IAAI,iBAAiB;AAC5G;AAAA,QACF;AACA,YAAI,gBAAgB,GAAG;AACrB,eAAK,KAAK,OAAO,GAAG,WAAW;AAAA,QACjC;AAAA,MACF;AA6EA,eAAS,cAAc,SAAS;AAC9B,aAAK,UAAU,KAAK,OAAO,CAAC,GAAG,cAAc,qBAAqB,OAAO;AACzE,aAAK,YAAY,CAAC;AAClB,aAAK,oBAAoB;AACzB,aAAK,qBAAqB;AAC1B,aAAK,uBAAuB;AAC5B,aAAK,qBAAqB;AAE1B,aAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,aAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AAAA,MACzC;AAGA,oBAAc,mBAAmB,SAAU,WAAW,MAAM;AACxD,YAAI,qBAAqB,KAAK,QAAQ,sBAAsB,cAAc,eAEtE,WAAW,SAAS,cAAc,KAAK,GACvC;AACJ,iBAAS,YAAY,SAAS;AAAA,UAC5B,mBAAmB,IAAI,KAAK,SAAS,CAAC;AAAA,QACxC,CAAC;AAED,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,kBAAQ,KAAK,CAAC,EAAE,OAAO,QAAQ,gBAAgB;AAC/C,cAAI,UAAU,IAAG;AACb,oBAAQ,QAAQ;AAAA,UACpB;AACA,cAAI,SAAS,SAAS,cAAc,MAAM;AAC1C,iBAAO,MAAM,QAAQ,KAAK,CAAC,EAAE,OAAO,QAAQ;AAC5C,iBAAO,YAAY,SAAS;AAAA,YAC1B,QAAQ,KAAK,QAAQ,cAAc,KAAK,CAAC,EAAE,OAAO,KAAK,QAAQ,OAAO,SAAS;AAAA,UACjF,CAAC;AACD,mBAAS,YAAY,SAAS,cAAc,IAAI,CAAC;AACjD,mBAAS,YAAY,MAAM;AAAA,QAC7B;AAEA,eAAO,SAAS;AAAA,MACpB;AAEA,oBAAc,sBAAsB;AAAA,QAClC,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,eAAe,SAAS,KAAK,WAAW;AACtC,iBAAO,WAAW,GAAG,EAAE,QAAQ,SAAS;AAAA,QAC1C;AAAA,QACA,eAAe,SAAS,KAAK,WAAW;AACtC,iBAAO,WAAW,GAAG,EAAE,QAAQ,SAAS;AAAA,QAC1C;AAAA,QACA,wBAAwB,SAAS,cAAc,WAAW;AACxD,iBAAO,WAAW,YAAY,EAAE,QAAQ,SAAS;AAAA,QACnD;AAAA,QACA,eAAe;AAAA,QACf,eAAe;AAAA,QACf,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,2BAA2B;AAAA,QAC3B,MAAM;AAAA,UACJ,WAAW;AAAA,UACX,aAAa;AAAA,UACb,WAAW;AAAA,UACX,eAAe;AAAA,UACf,kBAAkB;AAAA,UAClB,eAAe;AAAA,QACjB;AAAA,QACA,QAAQ;AAAA,UACN,WAAW;AAAA,UACX,UAAU;AAAA,UACV,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,wBAAwB;AAAA,UACxB,2BAA2B;AAAA,QAC7B;AAAA,QACA,OAAO;AAAA,UACL,MAAM;AAAA,UACN,WAAW;AAAA,UACX,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB,CAAC;AAAA,QAClB,SAAS;AAAA,QACT,aAAa;AAAA,UACX,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AAAA,QACA,kBAAkB,cAAc;AAAA,QAChC,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AAGA,oBAAc,uBAAwB,4BAAW;AAC/C,YAAI,wBAAwB,SAAS,UAAU,SAAS;AAClD,cAAIC,yBACF,OAAO,yBACP,OAAO,+BACP,OAAO,4BACP,OAAO,0BACP,OAAO,2BACP,SAASC,WAAU;AACjB,mBAAO,OAAO,WAAW,WAAW;AAClC,cAAAA,UAAS,KAAK,IAAI,CAAC;AAAA,YACrB,GAAG,EAAE;AAAA,UACP;AACF,iBAAOD,uBAAsB,KAAK,QAAQ,UAAU,OAAO;AAAA,QAC7D,GACA,uBAAuB,SAAS,IAAI;AAClC,cAAIE,wBACF,OAAO,wBACP,SAASC,KAAI;AACX,yBAAaA,GAAE;AAAA,UACjB;AACF,iBAAOD,sBAAqB,KAAK,QAAQ,EAAE;AAAA,QAC7C;AAEJ,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF,GAAG;AAEH,oBAAc,mCAAmC;AAAA,QAC/C,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAiBA,oBAAc,UAAU,gBAAgB,SAAS,YAAY,SAAS;AACpE,aAAK,UAAU,KAAK,EAAC,YAAwB,SAAS,KAAK,OAAO,CAAC,GAAG,cAAc,kCAAkC,OAAO,EAAC,CAAC;AAC/H,YAAI,WAAW,QAAQ,eAAe,WAAW,QAAQ,sBAAsB,GAAG;AAChF,qBAAW,qBAAqB;AAAA,YAC9B,WAAW;AACT,yBAAW,YAAY;AAAA,YACzB;AAAA,YACA,WAAW,QAAQ;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAKA,oBAAc,UAAU,mBAAmB,SAAS,YAAY;AAE9D,YAAI,YAAY,KAAK,UAAU;AAC/B,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAI,KAAK,UAAU,CAAC,EAAE,eAAe,YAAY;AAC/C,iBAAK,UAAU,OAAO,GAAG,CAAC;AAC1B;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW,oBAAoB;AAEjC,wBAAc,WAAW,kBAAkB;AAAA,QAC7C;AAAA,MACF;AAQA,oBAAc,UAAU,uBAAuB,SAAS,YAAY;AAElE,YAAI,YAAY,KAAK,UAAU;AAC/B,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAI,KAAK,UAAU,CAAC,EAAE,eAAe,YAAY;AAC/C,mBAAO,KAAK,UAAU,CAAC,EAAE;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAKA,oBAAc,UAAU,eAAe,SAAS,YAAY;AAE1D,YAAI,YAAY,KAAK,UAAU;AAC/B,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAI,KAAK,UAAU,CAAC,EAAE,eAAe,YAAY;AAC/C,gBAAI,MAAM,KAAK,UAAU,OAAO,GAAG,CAAC;AACpC,iBAAK,UAAU,KAAK,IAAI,CAAC,CAAC;AAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AASA,oBAAc,UAAU,WAAW,SAAS,QAAQ,aAAa;AAC/D,aAAK,SAAS;AAEd,aAAK,cAAc,SAAS,KAAK,OAAO,aAAa,OAAO,CAAC;AAC7D,aAAK,eAAe,SAAS,KAAK,OAAO,aAAa,QAAQ,CAAC;AAE/D,aAAK,QAAQ;AACb,aAAK,MAAM;AAAA,MACb;AAEA,oBAAc,UAAU,eAAe,WAAY;AAEjD,YAAI,CAAC,KAAK,WAAW;AACnB,eAAK,YAAY,SAAS,cAAc,KAAK;AAC7C,eAAK,UAAU,YAAY;AAC3B,eAAK,UAAU,MAAM,gBAAgB;AACrC,eAAK,UAAU,MAAM,WAAW;AAChC,eAAK,UAAU,MAAM,UAAU;AAC/B,mBAAS,KAAK,YAAY,KAAK,SAAS;AAAA,QAC1C;AACA,eAAO,KAAK;AAAA,MACd;AAEA,oBAAc,UAAU,gBAAgB,WAAY;AAClD,YAAG,CAAC,KAAK,QAAQ,SAAQ;AACxB;AAAA,QACD;AACA,YAAI,KAAK,KAAK,aAAa;AAE3B,YAAI,CAAC,KAAK,aAAa,CAAC,KAAK,QAAQ,SAAS;AAC5C,aAAG,MAAM,UAAU;AACnB;AAAA,QACF;AAEA,YAAI,OAAO,KAAK;AAGhB,YAAI,IAAI,KAAK,QAAQ,kBACjB,OAAO,KAAK,SAAS,KAAK,QAAQ,iBAClC,QAAQ,KAAK,cAAc,KAAK,UAAU,KAAK,QAAQ;AAE3D,YAAI,OAAO,CAAC;AAGZ,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,cAAI,aAAa,KAAK,UAAU,CAAC,EAAE;AACnC,cAAI,WAAW,UAAU;AACrB;AAAA,UACJ;AAGA,cAAI,WAAW,KAAK,aAAa,WAAW,MAAM,CAAC;AACnD,cAAI,WAAW,KAAK,WAAW,WAAW,KAAK,QAAQ;AACrD,iBAAK,KAAK,EAAE,QAAQ,KAAK,UAAU,CAAC,GAAG,OAAO,UAAU,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC,EAAE,CAAC;AAAA,UAC/F;AAAA,QACF;AAEA,YAAI,KAAK,QAAQ;AAGf,aAAG,YAAY,KAAK,QAAQ,iBAAiB,KAAK,MAAM,GAAG,IAAI;AAC/D,aAAG,MAAM,UAAU;AAAA,QACrB,OAAO;AACL,aAAG,MAAM,UAAU;AAAA,QACrB;AAAA,MACF;AAEA,oBAAc,UAAU,YAAY,SAAU,KAAK;AACjD,aAAK,YAAY;AACjB,aAAK,SAAS,IAAI;AAClB,aAAK,SAAS,IAAI;AAClB,aAAK,aAAa,IAAI;AACtB,aAAK,aAAa,IAAI;AACtB,YAAG,CAAC,KAAK,QAAQ,SAAQ;AACxB;AAAA,QACD;AACA,YAAI,KAAK,KAAK,aAAa;AAC3B,WAAG,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,IAAI;AAC7C,WAAG,MAAM,OAAO,KAAK,MAAM,KAAK,UAAU,IAAI;AAC9C,aAAK,cAAc;AAAA,MACrB;AAEA,oBAAc,UAAU,WAAW,WAAY;AAC7C,aAAK,YAAY;AACjB,aAAK,SAAS,KAAK,SAAS;AAC5B,YAAI,KAAK;AACP,eAAK,UAAU,MAAM,UAAU;AAAA,MACnC;AAKA,oBAAc,UAAU,SAAS,WAAY;AAC3C,YAAI,MAAM,CAAC,KAAK,QAAQ,oBAAoB,CAAC,SAAS,IAAI,OAAO,kBAC7D,OAAO;AACX,YAAI,KAAK,QAAQ,YAAY;AAG3B,kBAAQ,KAAK,OAAO;AACpB,mBAAS,KAAK,OAAO;AAErB,cAAI,UAAU,KAAK,WAAW;AAC5B,iBAAK,YAAY;AACjB,iBAAK,OAAO,aAAa,SAAU,KAAK,MAAM,QAAQ,GAAG,EAAG,SAAS,CAAC;AACtE,iBAAK,OAAO,WAAW,IAAI,EAAE,MAAM,KAAK,GAAG;AAAA,UAC7C;AACA,cAAI,WAAW,KAAK,YAAY;AAC9B,iBAAK,aAAa;AAClB,iBAAK,OAAO,aAAa,UAAW,KAAK,MAAM,SAAS,GAAG,EAAG,SAAS,CAAC;AACxE,iBAAK,OAAO,WAAW,IAAI,EAAE,MAAM,KAAK,GAAG;AAAA,UAC7C;AAEA,eAAK,cAAc;AACnB,eAAK,eAAe;AAAA,QACtB,OAAO;AACL,kBAAQ,SAAS,KAAK,OAAO,aAAa,OAAO,CAAC;AAClD,mBAAS,SAAS,KAAK,OAAO,aAAa,QAAQ,CAAC;AAEpD,cAAI,QAAQ,GAAG;AAIb,gBAAI,KAAK,MAAM,KAAK,cAAc,GAAG,MAAM,OAAO;AAChD,mBAAK,OAAO,aAAa,SAAU,KAAK,MAAM,QAAQ,GAAG,EAAG,SAAS,CAAC;AACtE,mBAAK,OAAO,MAAM,QAAQ,QAAQ;AAClC,mBAAK,cAAc;AACnB,mBAAK,OAAO,WAAW,IAAI,EAAE,MAAM,KAAK,GAAG;AAAA,YAC7C;AAEA,gBAAI,KAAK,MAAM,KAAK,eAAe,GAAG,MAAM,QAAQ;AAClD,mBAAK,OAAO,aAAa,UAAW,KAAK,MAAM,SAAS,GAAG,EAAG,SAAS,CAAC;AACxE,mBAAK,OAAO,MAAM,SAAS,SAAS;AACpC,mBAAK,eAAe;AACpB,mBAAK,OAAO,WAAW,IAAI,EAAE,MAAM,KAAK,GAAG;AAAA,YAC7C;AAAA,UACF,OAAO;AACL,iBAAK,cAAc;AACnB,iBAAK,eAAe;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAKA,oBAAc,UAAU,QAAQ,WAAW;AACzC,YAAI,KAAK,OAAO;AAEd;AAAA,QACF;AAEA,aAAK,OAAO,iBAAiB,aAAa,KAAK,SAAS;AACxD,aAAK,OAAO,iBAAiB,YAAY,KAAK,QAAQ;AAGtD,YAAI,WAAU,WAAW;AACvB,eAAK,QAAQ,cAAc,qBAAqB,uBAAsB,WAAW;AAC/E,gBAAG,KAAK,QAAQ,iBAAgB;AAC7B,kBAAI,WAAW,oBAAI,KAAK,CAAC;AAEzB,kBAAI,eAAe,KAAK,UAAU,QAAO,SAAS,KAAK,QAAO;AAC5D,oBAAI,UAAU,OAAO,WAAW;AAChC,oBAAI,eAAe,KAAK,MAAM,KAAK,QAAQ,4BAA4B,QAAQ,MAAM,IAAI;AACzF,+BAAe,gBAAgB,IAAI,eAAe;AAClD,+BAAe,gBAAgB,QAAQ,SAAQ,IAAI,eAAe,QAAQ,SAAQ;AAClF,oBAAG,WAAW,QAAQ,SAAS,GAC/B;AAEC,sBAAI,oBAAoB,QAAQ,YAAY,EAAE,CAAC;AAC/C,wBAAM,MAAM,oBAAoB,MAAM;AAAA,gBACvC;AACA,uBAAO;AAAA,cACV,GAAE,KAAK,IAAI,GAAG,QAAQ;AAEtB,mBAAK,OAAO,KAAK,QAAQ,eAAe,WAAW,eAAe,IAAI;AAAA,YACxE,OAAO;AACL,mBAAK,OAAO;AAAA,YACd;AACA,oBAAQ;AAAA,UACV,GAAE,KAAK,IAAI,CAAC;AAAA,QACd,GAAE,KAAK,IAAI;AAEX,gBAAQ;AAAA,MACV;AAKA,oBAAc,UAAU,OAAO,WAAW;AACxC,YAAI,KAAK,OAAO;AACd,wBAAc,qBAAqB,qBAAqB,KAAK,KAAK;AAClE,iBAAO,KAAK;AACZ,eAAK,OAAO,oBAAoB,aAAa,KAAK,SAAS;AAC3D,eAAK,OAAO,oBAAoB,YAAY,KAAK,QAAQ;AAAA,QAC3D;AAAA,MACF;AAEA,oBAAc,UAAU,mBAAmB,WAAW;AAEpD,YAAI,eAAe,KAAK,SACpB,gBAAgB,OAAO,KACvB,gBAAgB,OAAO;AAE3B,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAE9C,cAAI,aAAa,KAAK,UAAU,CAAC,EAAE;AACnC,cAAI,WAAW,UAAU;AACrB;AAAA,UACJ;AAEA,cAAI,CAAC,MAAM,WAAW,QAAQ,GAAG;AAC/B,4BAAgB,CAAC,MAAM,aAAa,IAAI,KAAK,IAAI,eAAe,WAAW,QAAQ,IAAI,WAAW;AAAA,UACpG;AAEA,cAAI,CAAC,MAAM,WAAW,QAAQ,GAAG;AAC/B,4BAAgB,CAAC,MAAM,aAAa,IAAI,KAAK,IAAI,eAAe,WAAW,QAAQ,IAAI,WAAW;AAAA,UACpG;AAAA,QACF;AAGA,YAAI,aAAa,YAAY,MAAM;AACjC,0BAAgB,aAAa;AAAA,QAC/B,OAAO;AACL,2BAAiB,aAAa;AAAA,QAChC;AAGA,YAAI,aAAa,YAAY,MAAM;AACjC,0BAAgB,aAAa;AAAA,QAC/B,OAAO;AACL,2BAAiB,KAAK,IAAI,gBAAgB,aAAa,gBAAgB,aAAa;AAAA,QACtF;AAGA,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,cAAI,QAAQ,KAAK,QAAQ,eAAe,EAAC,KAAK,eAAe,KAAK,cAAa,CAAC;AAChF,0BAAgB,MAAM;AACtB,0BAAgB,MAAM;AAAA,QACxB;AAEA,YAAI,CAAC,MAAM,aAAa,KAAK,CAAC,MAAM,aAAa,GAAG;AAClD,cAAI,mBAAmB,gBAAgB;AACvC,cAAI,iBAAkB,mBAAmB,KAAK;AAC9C,cAAI,eAAgB,gBAAgB,KAAK;AACzC,eAAK,mBAAmB,KAAK,IAAI,cAAc,IAAI,OAAO,KAAK,IAAI,YAAY,IAAI;AACnF,eAAK,qBAAqB,aAAa,iBAAiB;AACxD,eAAK,sBAAsB,aAAa,iBAAiB;AAAA,QAC3D;AAEA,aAAK,aAAa,EAAE,KAAK,eAAe,KAAK,cAAc;AAAA,MAC7D;AAEA,oBAAc,UAAU,SAAS,SAAS,QAAQ,MAAM;AACtD,YAAI,YAAY,KAAK,IAAI;AAGzB,YAAI,KAAK,QAAQ,WAAW,KAAK,YAAY,KAAK,uBAAwB,MAAK,KAAK,QAAQ;AAC1F;AAEF,gBAAQ,QAAQ,cAAc,KAAK,SAAS;AAG5C,gBAAQ,OAAO,KAAK,QAAQ;AAE5B,YAAI,CAAC,KAAK,kBAAkB;AAG1B,cAAI,WAAW,KAAK,uBAAuB;AAC3C,cAAI,UAAU;AAGZ,gBAAI,kCAAkC,YAAY,KAAK,uBAAuB,MAAK;AACnF,gBAAI,CAAC,iCAAiC;AACpC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,aAAK,uBAAuB;AAC5B,aAAK,qBAAqB;AAE1B,aAAK,OAAO;AAEZ,iBAAS,UAAU,KAAK;AACxB,YAAI,UAAU,OAAO,WAAW,IAAI,GAChC,eAAe,KAAK,SAEpB,aAAa,EAAE,KAAK,GAAG,MAAM,GAAG,OAAO,KAAK,aAAa,QAAQ,KAAK,aAAa,GAEnF,kBAAkB,OAAQ,WAAW,QAAQ,aAAa,gBAC1D,oBAAmB,SAAS,OAAOE,YAAW;AAC5C,cAAI,SAAS,QAAQ,KAAK,oBACtB,YAAY,KAAK,sBAAsB,IACnC,WAAW,SACX,WAAW,UAAU,IAAI,SAAS,KAAK;AAC/C,iBAAO,KAAK,UAAU,WAAWA,UAAS;AAAA,QAC5C,GAAE,KAAK,IAAI,GACX,kBAAkB,SAASC,IAAGD,YAAW;AACvC,cAAI,YAAY,aAAa,mBACxB,OAAOC,MAAK,aAAa,iBAC1B,WAAW,SAAU,OAAOA,MAAK,aAAa;AAClD,iBAAO,KAAK,UAAU,WAAWD,UAAS;AAAA,QAC5C;AAEJ,aAAK,iBAAiB;AAEtB,gBAAQ,OAAO,aAAa,OAAO,WAAW,QAAQ,aAAa,OAAO;AAI1E,gBAAQ,KAAK;AAGb,gBAAQ,UAAU,WAAW,MAAM,WAAW,GAAG;AAKjD,gBAAQ,UAAU;AAClB,gBAAQ,KAAK,GAAG,GAAG,WAAW,OAAO,WAAW,MAAM;AACtD,gBAAQ,KAAK;AAGb,gBAAQ,KAAK;AACb,gBAAQ,YAAY,aAAa,KAAK;AACtC,gBAAQ,UAAU,GAAG,GAAG,WAAW,OAAO,WAAW,MAAM;AAC3D,gBAAQ,SAAS,GAAG,GAAG,WAAW,OAAO,WAAW,MAAM;AAC1D,gBAAQ,QAAQ;AAGhB,gBAAQ,KAAK;AACb,gBAAQ,YAAY,aAAa,KAAK;AACtC,gBAAQ,cAAc,aAAa,KAAK;AAExC,YAAI,aAAa,KAAK,gBAAgB,GAAG;AACvC,kBAAQ,UAAU;AAClB,mBAAS,IAAI,OAAQ,OAAO,aAAa,KAAK,eACzC,KAAK,iBACL,KAAK,aAAa,KAAK,eAAe;AACzC,gBAAI,KAAK,gBAAgB,GAAG,aAAa,KAAK,SAAS;AACvD,oBAAQ,OAAO,IAAI,CAAC;AACpB,oBAAQ,OAAO,IAAI,WAAW,MAAM;AAAA,UACtC;AACA,kBAAQ,OAAO;AACf,kBAAQ,UAAU;AAAA,QACpB;AAGA,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK,kBAAkB,KAAK;AAC3D,cAAI,KAAK,KAAK,UAAU,IAAI,WAAW,SAAS,aAAa,KAAK,kBAAkB,aAAa,KAAK,SAAS;AAC/G,kBAAQ,UAAU;AAClB,kBAAQ,OAAO,GAAG,EAAE;AACpB,kBAAQ,OAAO,WAAW,OAAO,EAAE;AACnC,kBAAQ,OAAO;AACf,kBAAQ,UAAU;AAAA,QACpB;AAEA,YAAI,aAAa,KAAK,eAAe;AACnC,kBAAQ,UAAU;AAClB,kBAAQ,WAAW,GAAG,GAAG,WAAW,OAAO,WAAW,MAAM;AAC5D,kBAAQ,UAAU;AAAA,QACpB;AACA,gBAAQ,QAAQ;AAGhB,YAAI,aAAa,mBAAmB,aAAa,gBAAgB,QAAQ;AACvE,mBAAS,KAAK,GAAG,KAAK,aAAa,gBAAgB,QAAQ,MAAM;AAC/D,gBAAI,OAAO,aAAa,gBAAgB,EAAE,GACtC,YAAY,KAAK,aAAa,GAC9B,MAAM,iBAAiB,KAAK,OAAO,SAAS;AAChD,oBAAQ,cAAc,KAAK,SAAS;AACpC,oBAAQ,YAAY;AACpB,oBAAQ,UAAU;AAClB,oBAAQ,OAAO,GAAG,GAAG;AACrB,oBAAQ,OAAO,WAAW,OAAO,GAAG;AACpC,oBAAQ,OAAO;AACf,oBAAQ,UAAU;AAAA,UACpB;AAAA,QACF;AAGA,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,cAAI,aAAa,KAAK,UAAU,CAAC,EAAE,YAC/B,UAAU,WAAW;AAGzB,qBAAW,YAAY,iBAAiB,aAAa,gBAAgB;AACrE,cAAI,QAAQ,UAAU,KAAK,WAAW,UAAU;AAC5C;AAAA,UACJ;AACA,kBAAQ,KAAK;AAEb,cAAI,gBAAgB,KAAK,UAAU,CAAC,EAAE,SAElC,aAAa,cAAc,eAAe,cAAc,gBAAgB,QACxE,qBAAqB,aAAa,cAAc,YAAY;AAGhE,kBAAQ,UAAU;AAElB,cAAI,SAAS,gBAAgB,QAAQ,CAAC,EAAE,CAAC,GAAG,kBAAkB,GAC5D,SAAS,iBAAiB,QAAQ,CAAC,EAAE,CAAC,GAAG,kBAAkB,GAC3D,QAAQ,QACR,QAAQ,QACR;AACF,kBAAQ,OAAO,QAAQ,MAAM;AAC7B,kBAAQ,cAAc,iBAAiB,aAAa,eAAe;AAAA,YACjE,KAAK;AAAA,YACL,KAAK,QAAQ;AACX,qBAAO,SAASE,IAAGC,IAAGC,QAAOC,QAAO;AAClC,wBAAQ,OAAOH,IAAEC,EAAC;AAAA,cACpB;AACA;AAAA,YACF;AAAA,YACA,KAAK;AAAA,YACL,SAAS;AAeP,qBAAO,SAASD,IAAGC,IAAGC,QAAOC,QAAO;AAClC,wBAAQ;AAAA;AAAA,kBACN,KAAK,OAAOD,SAAQF,MAAK,CAAC;AAAA,kBAAGG;AAAA;AAAA,kBAC7B,KAAK,MAAOD,SAAQF,EAAE,IAAI;AAAA,kBAAGC;AAAA;AAAA,kBAC7BD;AAAA,kBAAGC;AAAA,gBAAC;AAAA,cACR;AACA;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AACX,qBAAO,SAASD,IAAGC,IAAGC,QAAOC,QAAO;AAClC,wBAAQ,OAAOH,IAAEG,MAAK;AACtB,wBAAQ,OAAOH,IAAEC,EAAC;AAAA,cACpB;AACA;AAAA,YACF;AAAA,UACF;AAEA,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAI,UAAU,QAAQ,CAAC,GACnB,IAAI,gBAAgB,QAAQ,CAAC,GAAG,kBAAkB,GAClD,IAAI,iBAAiB,QAAQ,CAAC,GAAG,kBAAkB;AACvD,iBAAK,GAAG,GAAG,OAAO,KAAK;AACvB,oBAAQ;AAAG,oBAAQ;AAAA,UACrB;AAEA,cAAI,YAAY;AACd,oBAAQ,YAAY,cAAc;AAClC,oBAAQ,cAAc,cAAc;AACpC,oBAAQ,OAAO;AAAA,UACjB;AAEA,cAAI,cAAc,WAAW;AAE3B,oBAAQ,OAAO,OAAO,WAAW,SAAS,qBAAqB,CAAC;AAChE,oBAAQ,OAAO,QAAQ,WAAW,SAAS,qBAAqB,CAAC;AAEjE,oBAAQ,YAAY,cAAc;AAClC,oBAAQ,KAAK;AAAA,UACf;AAEA,kBAAQ,QAAQ;AAAA,QAClB;AAEA,YAAI,aAAa,WAAW,KAAK,UAAU,GAAG;AAE5C,kBAAQ,YAAY,aAAa,YAAY;AAC7C,kBAAQ,cAAc,aAAa,YAAY;AAC/C,kBAAQ,UAAU;AAClB,kBAAQ,OAAO,KAAK,QAAQ,CAAC;AAC7B,kBAAQ,OAAO,KAAK,QAAQ,WAAW,MAAM;AAC7C,kBAAQ,UAAU;AAClB,kBAAQ,OAAO;AAAA,QACjB;AACA,aAAK,cAAc;AAEnB,YAAI,gBAAgB,aAAa;AAEjC,YAAI,CAAC,cAAc,YAAY,CAAC,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,MAAM,KAAK,WAAW,GAAG,GAAG;AACzF,cAAI,iBAAiB,aAAa,cAAc,KAAK,WAAW,KAAK,cAAc,SAAS,GACxF,iBAAiB,aAAa,cAAc,KAAK,WAAW,KAAK,cAAc,SAAS,GACxF,cAAc,aAAa,kBAAkB,IAAI,WAAW,QAAQ,QAAQ,YAAY,cAAc,EAAE,QAAQ,GAChH,cAAc,aAAa,kBAAkB,IAAI,WAAW,QAAQ,QAAQ,YAAY,cAAc,EAAE,QAAQ;AACpH,kBAAQ,YAAY,cAAc;AAClC,kBAAQ,SAAS,gBAAgB,aAAa,cAAc,QAAQ;AACpE,kBAAQ,SAAS,gBAAgB,aAAa,WAAW,SAAS,CAAC;AAAA,QACrE;AAGA,YAAK,cAAc,0BACV,CAAC,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,MAAM,KAAK,WAAW,GAAG,KACzD,aAAa,KAAK,mBAAmB,GAAG;AAE/C,cAAI,QAAQ,KAAK,WAAW,MAAM,KAAK,WAAW,OAAO,aAAa,KAAK;AAC3E,cAAI,aAAa,WAAW,SAAS,aAAa,KAAK;AACvD,mBAAS,IAAI,GAAG,IAAI,aAAa,KAAK,kBAAkB,KAAK;AAC3D,gBAAI,KAAK,WAAW,SAAS,KAAK,MAAM,IAAI,UAAU,GAClD,SAAS,aAAa,uBAAuB,KAAK,WAAW,MAAO,IAAI,MAAO,cAAc,SAAS,GAEtG,uBACE,cAAc,4BACX,aAAa,kBAAkB,IAAI,WAAW,QAAQ,QAAQ,YAAY,MAAM,EAAE,QAAQ,IAC1F,aAAa,kBAAkB,WAAW,QAAQ,QAAQ,YAAY,MAAM,EAAE,QAAQ,IAAI;AAEnG,oBAAQ,SAAS,QAAQ,sBAAsB,KAAK,aAAa,KAAK,SAAS;AAAA,UACjF;AAAA,QACF;AAGA,YAAI,aAAa,sBAAsB,aAAa,KAAK,gBAAgB,GAAG;AAC1E,cAAI,aAAa,aAAa,kBAC1B,QAAQ,YAAY,cAAc,EAAE,QACpC,WAAW,QAAQ,QAAQ,YAAY,cAAc,EAAE,QAAQ;AACnE,mBAAS,IAAI,OAAQ,OAAO,aAAa,KAAK,eACzC,KAAK,iBACL,KAAK,aAAa,KAAK,eAAe;AACzC,gBAAI,KAAK,gBAAgB,GAAG,CAAC;AAE7B,gBAAK,CAAC,aAAa,mBAAmB,KAAK,cAAgB,aAAa,mBAAmB,KAAK,YAAc;AAG5G,kBAAI,KAAK,IAAI,KAAK,CAAC,GACjB,KAAK,aAAa,mBAAmB,EAAE,GACvC,UAAU,QAAQ,YAAY,EAAE,EAAE;AAEpC,2BAAa,aAAa,kBACtB,KAAK,UAAU,IACf,KAAK,UAAU;AAEnB,sBAAQ,YAAY,aAAa,OAAO;AACxC,kBAAG,aAAa,iBAAiB;AAC/B,wBAAQ,SAAS,IAAI,IAAI,WAAW,SAAS,CAAC;AAAA,cAChD,OAAO;AACL,wBAAQ,SAAS,IAAI,KAAK,SAAS,WAAW,SAAS,CAAC;AAAA,cAC1D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAGA,YAAI,aAAa,MAAM,SAAS,IAAI;AAClC,kBAAQ,OAAO,aAAa,MAAM,WAAW,QAAQ,aAAa,MAAM;AACxE,cAAI,YAAY,aAAa,kBAAkB,WAAW,QAAQ,QAAQ,YAAY,aAAa,MAAM,IAAI,EAAE,QAAQ,IAAI;AAC3H,cAAI,aAAa,MAAM,iBAAiB,UAAU;AAChD,oBAAQ,eAAe;AACvB,gBAAI,YAAY,WAAW;AAAA,UAC7B,WAAW,aAAa,MAAM,iBAAiB,UAAU;AACvD,oBAAQ,eAAe;AACvB,gBAAI,YAAY,WAAW,SAAS;AAAA,UACtC,OAAO;AACL,oBAAQ,eAAe;AACvB,gBAAI,YAAY;AAAA,UAClB;AACA,kBAAQ,YAAY,aAAa,MAAM;AACvC,kBAAQ,SAAS,aAAa,MAAM,MAAM,WAAW,SAAS;AAAA,QAChE;AAEA,gBAAQ,QAAQ;AAAA,MAClB;AAGA,oBAAc,gBAAgB,SAAS,MAAM;AAC3C,iBAAS,KAAK,QAAQ;AAAE,kBAAQ,SAAS,KAAK,MAAM,MAAM;AAAA,QAAO;AACjE,eAAO,KAAK,KAAK,SAAS,CAAC,IAAI,MAAM,KAAK,KAAK,WAAW,CAAC,IAAI,MAAM,KAAK,KAAK,WAAW,CAAC;AAAA,MAC7F;AAEA,MAAAR,SAAQ,aAAa;AACrB,MAAAA,SAAQ,gBAAgB;AAAA,IAE1B,GAAG,OAAO,YAAY,cAAc,UAAO,OAAO;AAAA;AAAA;", "names": ["exports", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "lineWidth", "t", "x", "y", "lastX", "lastY"]}